# New /indicators Endpoint Implementation

## Overview

A new comprehensive API endpoint has been added to provide candlestick data along with all major technical indicators in a single request. This endpoint uses the `ta` (Technical Analysis) library to calculate 43 different technical indicators across 5 categories.

## What Was Implemented

### 1. New Dependencies
- Added `ta==0.10.2` to `requirements.txt`
- Technical Analysis library for Python with comprehensive indicator support

### 2. New Module: `app/comprehensive_indicators.py`
- `ComprehensiveIndicators` class extending `Data_Sourcing`
- Calculates all technical indicators using the ta library
- Organized indicators by category (Volume, Volatility, Trend, Momentum, Others)
- Handles data conversion and error management

### 3. New API Endpoint: `/indicators`
- **Methods**: GET and POST
- **Authentication**: X-API-Key header required
- **Response**: JSON with candlesticks and indicators

### 4. Updated Documentation
- Added endpoint documentation to the HTML template in `Trade.py`
- Updated `README.md` with new endpoint information
- Created comprehensive `INDICATORS.md` reference guide

### 5. Example Files and Tests
- `test_indicators_endpoint.py` - Test script for the new endpoint
- `examples/indicators_usage.py` - Usage examples and client class
- `install_ta_library.py` - Installation helper script

## API Endpoint Details

### Request Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `asset` | string | No | "Cryptocurrency" | Asset type |
| `equity` | string | **Yes** | - | Specific instrument (e.g., "BTC", "AAPL") |
| `market` | string | No | "USDT" | Market type |
| `exchange` | string | No | "Binance" | Exchange (for crypto) |
| `bar` | string | No | "1m" | Time interval |
| `limit` | integer | No | 100 | Number of candlesticks (max 100) |
| `indicatorName` | string | No | "all" | Indicators to calculate |
| `after` | string | No | - | Pagination (future feature) |
| `before` | string | No | - | Pagination (future feature) |

### Example Request

```bash
curl -X POST http://localhost:5000/indicators \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-api-key-12345" \
  -d '{
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "bar": "1H",
    "limit": 50,
    "indicatorName": "all"
  }'
```

### Response Structure

```json
{
  "status": "success",
  "data": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "interval": "1H",
    "candlesticks": [
      {
        "timestamp": "2024-01-01T00:00:00",
        "open": 42000.0,
        "high": 42500.0,
        "low": 41800.0,
        "close": 42300.0,
        "volume": 1234.56
      }
    ],
    "indicators": {
      "volume": { /* 9 volume indicators */ },
      "volatility": { /* 5 volatility indicators */ },
      "trend": { /* 15 trend indicators */ },
      "momentum": { /* 11 momentum indicators */ },
      "others": { /* 3 other indicators */ }
    }
  },
  "metadata": {
    "total_records": 50,
    "limit": 50,
    "requested_indicators": "all",
    "has_more": false
  }
}
```

## Technical Indicators Included

### Volume Indicators (9)
| Indicator | Function Name | Default Parameters |
|-----------|---------------|-------------------|
| Money Flow Index (MFI) | `money_flow_index` | window=14 |
| Accumulation/Distribution Index (ADI) | `acc_dist_index` | No parameters |
| On-Balance Volume (OBV) | `on_balance_volume` | No parameters |
| Chaikin Money Flow (CMF) | `chaikin_money_flow` | window=20 |
| Force Index (FI) | `force_index` | window=13 |
| Ease of Movement (EoM, EMV) | `ease_of_movement`, `sma_ease_of_movement` | window=14 |
| Volume-price Trend (VPT) | `volume_price_trend` | No parameters |
| Negative Volume Index (NVI) | `negative_volume_index` | No parameters |
| Volume Weighted Average Price (VWAP) | `volume_weighted_average_price` | Calculated manually |

### Volatility Indicators (5)
| Indicator | Function Name | Default Parameters |
|-----------|---------------|-------------------|
| Average True Range (ATR) | `average_true_range` | window=14 |
| Bollinger Bands (BB) | `bollinger_hband`, `bollinger_lband`, `bollinger_mavg`, etc. | window=20, window_dev=2 |
| Keltner Channel (KC) | `keltner_channel_hband`, `keltner_channel_lband`, etc. | window=20, window_atr=10, multiplier=2 |
| Donchian Channel (DC) | `donchian_channel_hband`, `donchian_channel_lband`, etc. | window=20, offset=0 |
| Ulcer Index (UI) | `ulcer_index` | window=14 |

### Trend Indicators (15)
| Indicator | Function Name | Default Parameters |
|-----------|---------------|-------------------|
| Simple Moving Average (SMA) | `sma_indicator` | **window=20** (custom) |
| Exponential Moving Average (EMA) | `ema_indicator` | window=14 |
| Weighted Moving Average (WMA) | `wma_indicator` | **window=14** (custom) |
| MACD | `macd`, `macd_diff`, `macd_signal` | window_fast=12, window_slow=26, window_sign=9 |
| ADX | `adx`, `adx_neg`, `adx_pos` | window=14 |
| Vortex Indicator (VI) | `vortex_indicator_neg`, `vortex_indicator_pos` | window=14 |
| TRIX | `trix` | window=15 |
| Mass Index (MI) | `mass_index` | **window_fast=9, window_slow=25** (custom) |
| Commodity Channel Index (CCI) | `cci` | window=20, constant=0.015 |
| Detrended Price Oscillator (DPO) | `dpo` | window=20 |
| KST Oscillator | `kst`, `kst_sig` | Default KST parameters |
| Ichimoku | `ichimoku_a`, `ichimoku_b`, `ichimoku_base_line`, `ichimoku_conversion_line` | **window1=9, window2=26, window3=52** (custom) |
| Parabolic SAR | `psar_down`, `psar_up`, `psar_down_indicator`, `psar_up_indicator` | step=0.02, max_step=0.2 |
| Schaff Trend Cycle (STC) | `stc` | Default STC parameters |
| Aroon Indicator | `aroon_down`, `aroon_up` | window=25 |

### Momentum Indicators (11)
| Indicator | Function Name | Default Parameters |
|-----------|---------------|-------------------|
| Relative Strength Index (RSI) | `rsi` | window=14 |
| Stochastic RSI | `stochrsi`, `stochrsi_d`, `stochrsi_k` | window=14, smooth1=3, smooth2=3 |
| True Strength Index (TSI) | `tsi` | window_slow=25, window_fast=13 |
| Ultimate Oscillator | `ultimate_oscillator` | window1=7, window2=14, window3=28, weight1=4.0, weight2=2.0, weight3=1.0 |
| Stochastic Oscillator | `stoch`, `stoch_signal` | window=14, smooth_window=3 |
| Williams %R | `williams_r` | lbp=14 |
| Awesome Oscillator | `awesome_oscillator` | window1=5, window2=34 |
| KAMA | `kama` | window=10, pow1=2, pow2=30 |
| Rate of Change (ROC) | `roc` | window=12 |
| Percentage Price Oscillator (PPO) | `ppo`, `ppo_hist`, `ppo_signal` | window_slow=26, window_fast=12, window_sign=9 |
| Percentage Volume Oscillator (PVO) | `pvo`, `pvo_hist`, `pvo_signal` | window_slow=26, window_fast=12, window_sign=9 |

### Other Indicators (3)
| Indicator | Function Name | Default Parameters |
|-----------|---------------|-------------------|
| Daily Return | `daily_return` | No parameters |
| Daily Log Return | `daily_log_return` | No parameters |
| Cumulative Return | `cumulative_return` | No parameters |

## Default Parameter Notes

### 📝 **Important Notes About Default Settings**

1. **Custom Parameters** (marked with **bold**):
   - **SMA window=20**: Set to 20 periods instead of ta library default
   - **WMA window=14**: Explicitly set for consistency
   - **Mass Index**: Custom windows (fast=9, slow=25) for better sensitivity
   - **Ichimoku**: Traditional settings (9, 26, 52) for standard analysis

2. **Standard Parameters**:
   - Most indicators use the ta library's default parameters
   - These are industry-standard settings used by most trading platforms
   - RSI: 14 periods (most common setting)
   - MACD: 12, 26, 9 (standard fast, slow, signal)
   - Bollinger Bands: 20 periods, 2 standard deviations

3. **No Customization Available**:
   - The current implementation uses fixed parameters
   - Future versions may allow parameter customization via API
   - Parameters are optimized for general-purpose technical analysis

### 🔧 **Parameter Customization (Future Enhancement)**

Currently, all indicators use their default parameters. A future enhancement could allow:

```json
{
  "indicatorName": "rsi",
  "indicatorParams": {
    "rsi": {"window": 21}
  }
}
```

## Installation and Setup

1. **Install the ta library**:
   ```bash
   python install_ta_library.py
   ```

2. **Start the API**:
   ```bash
   python Trade.py
   ```

3. **Test the endpoint**:
   ```bash
   python test_indicators_endpoint.py
   ```

## Usage Examples

### Get All Indicators
```python
import requests

response = requests.post('http://localhost:5000/indicators', 
    headers={'X-API-Key': 'test-api-key-12345', 'Content-Type': 'application/json'},
    json={
        'asset': 'Cryptocurrency',
        'equity': 'BTC',
        'market': 'USDT',
        'indicatorName': 'all'
    })
```

### Get Specific Indicators
```python
response = requests.post('http://localhost:5000/indicators',
    headers={'X-API-Key': 'test-api-key-12345', 'Content-Type': 'application/json'},
    json={
        'asset': 'Cryptocurrency',
        'equity': 'ETH',
        'market': 'USDT',
        'indicatorName': 'rsi,macd,bollinger_hband,sma_indicator'
    })
```

## Key Features

1. **Single API Call**: Get candlesticks + all indicators in one request
2. **Comprehensive Coverage**: 43 technical indicators across 5 categories
3. **Flexible Selection**: Request all indicators or specific ones
4. **Multiple Assets**: Supports crypto, stocks, forex, futures, indices
5. **Multiple Exchanges**: Binance, OKX, Yahoo Finance
6. **Time Intervals**: 1m, 3m, 5m, 15m, 30m, 1H, 2H, 4H, 1D
7. **Pagination Ready**: Framework for future pagination implementation

## Files Modified/Created

### Modified Files
- `requirements.txt` - Added ta library
- `Trade.py` - Added new endpoint and documentation
- `README.md` - Updated with new endpoint info
- `app/data_sourcing.py` - Modified apis() method to accept limit parameter

### New Files
- `app/comprehensive_indicators.py` - Main implementation
- `test_indicators_endpoint.py` - Test script
- `examples/indicators_usage.py` - Usage examples
- `install_ta_library.py` - Installation helper
- `INDICATORS.md` - Complete reference guide
- `NEW_INDICATORS_ENDPOINT.md` - This summary document

## Next Steps

1. Install the ta library: `python install_ta_library.py`
2. Test the implementation: `python test_indicators_endpoint.py`
3. Try the examples: `python examples/indicators_usage.py`
4. Integrate into your trading workflows
5. Consider implementing pagination for large datasets
6. Add caching for frequently requested indicators

The new endpoint provides a comprehensive solution for technical analysis, combining market data with all major indicators in a single, efficient API call.

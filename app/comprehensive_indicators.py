import pandas as pd
import numpy as np
from app.data_sourcing import Data_Sourcing
import ta
from ta.volume import *
from ta.volatility import *
from ta.trend import *
from ta.momentum import *
from ta.others import *

class ComprehensiveIndicators(Data_Sourcing):
    """
    Comprehensive technical analysis using the ta library
    Supports all major technical indicators across different categories
    """
    
    def __init__(self, exchange, interval, asset, market=None, limit=100):
        super().__init__()
        super().exchange_data(exchange)
        if market:
            super().market_data(market)
        super().intervals(interval)
        super().apis(asset, limit=limit)
        
        # Check if we have sufficient data for calculations
        if self.df.empty:
            raise ValueError(f"No market data available for {asset}. Cannot perform technical analysis.")
        
        # Ensure we have the required columns
        required_columns = ['High', 'Low', 'Open', 'Volume', 'Adj Close']
        missing_columns = [col for col in required_columns if col not in self.df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
    
    def calculate_volume_indicators(self):
        """Calculate all volume-based technical indicators"""
        high = self.df['High']
        low = self.df['Low']
        close = self.df['Adj Close']
        volume = self.df['Volume']
        
        volume_indicators = {}
        
        try:
            # Money Flow Index (MFI)
            volume_indicators['money_flow_index'] = ta.volume.MFIIndicator(
                high=high, low=low, close=close, volume=volume
            ).money_flow_index()
            
            # Accumulation/Distribution Index (ADI)
            volume_indicators['acc_dist_index'] = ta.volume.AccDistIndexIndicator(
                high=high, low=low, close=close, volume=volume
            ).acc_dist_index()
            
            # On-Balance Volume (OBV)
            volume_indicators['on_balance_volume'] = ta.volume.OnBalanceVolumeIndicator(
                close=close, volume=volume
            ).on_balance_volume()
            
            # Chaikin Money Flow (CMF)
            volume_indicators['chaikin_money_flow'] = ta.volume.ChaikinMoneyFlowIndicator(
                high=high, low=low, close=close, volume=volume
            ).chaikin_money_flow()
            
            # Force Index (FI)
            volume_indicators['force_index'] = ta.volume.ForceIndexIndicator(
                close=close, volume=volume
            ).force_index()
            
            # Ease of Movement (EoM, EMV)
            eom_indicator = ta.volume.EaseOfMovementIndicator(
                high=high, low=low, volume=volume
            )
            volume_indicators['ease_of_movement'] = eom_indicator.ease_of_movement()
            volume_indicators['sma_ease_of_movement'] = eom_indicator.sma_ease_of_movement()
            
            # Volume-price Trend (VPT)
            volume_indicators['volume_price_trend'] = ta.volume.VolumePriceTrendIndicator(
                close=close, volume=volume
            ).volume_price_trend()
            
            # Negative Volume Index (NVI)
            volume_indicators['negative_volume_index'] = ta.volume.NegativeVolumeIndexIndicator(
                close=close, volume=volume
            ).negative_volume_index()
            
            # Volume Weighted Average Price (VWAP) - using simple volume-weighted calculation
            # Note: ta library doesn't have a direct VWAP, so we calculate it manually
            typical_price = (high + low + close) / 3
            volume_indicators['volume_weighted_average_price'] = (typical_price * volume).cumsum() / volume.cumsum()
            
        except Exception as e:
            print(f"Error calculating volume indicators: {e}")
        
        return volume_indicators
    
    def calculate_volatility_indicators(self):
        """Calculate all volatility-based technical indicators"""
        high = self.df['High']
        low = self.df['Low']
        close = self.df['Adj Close']
        
        volatility_indicators = {}
        
        try:
            # Average True Range (ATR)
            volatility_indicators['average_true_range'] = ta.volatility.AverageTrueRange(
                high=high, low=low, close=close
            ).average_true_range()
            
            # Bollinger Bands (BB)
            bb_indicator = ta.volatility.BollingerBands(close=close)
            volatility_indicators['bollinger_hband'] = bb_indicator.bollinger_hband()
            volatility_indicators['bollinger_hband_indicator'] = bb_indicator.bollinger_hband_indicator()
            volatility_indicators['bollinger_lband'] = bb_indicator.bollinger_lband()
            volatility_indicators['bollinger_lband_indicator'] = bb_indicator.bollinger_lband_indicator()
            volatility_indicators['bollinger_mavg'] = bb_indicator.bollinger_mavg()
            volatility_indicators['bollinger_pband'] = bb_indicator.bollinger_pband()
            volatility_indicators['bollinger_wband'] = bb_indicator.bollinger_wband()
            
            # Keltner Channel (KC)
            kc_indicator = ta.volatility.KeltnerChannel(high=high, low=low, close=close)
            volatility_indicators['keltner_channel_hband'] = kc_indicator.keltner_channel_hband()
            volatility_indicators['keltner_channel_hband_indicator'] = kc_indicator.keltner_channel_hband_indicator()
            volatility_indicators['keltner_channel_lband'] = kc_indicator.keltner_channel_lband()
            volatility_indicators['keltner_channel_lband_indicator'] = kc_indicator.keltner_channel_lband_indicator()
            volatility_indicators['keltner_channel_mband'] = kc_indicator.keltner_channel_mband()
            volatility_indicators['keltner_channel_pband'] = kc_indicator.keltner_channel_pband()
            volatility_indicators['keltner_channel_wband'] = kc_indicator.keltner_channel_wband()
            
            # Donchian Channel (DC)
            dc_indicator = ta.volatility.DonchianChannel(high=high, low=low, close=close)
            volatility_indicators['donchian_channel_hband'] = dc_indicator.donchian_channel_hband()
            volatility_indicators['donchian_channel_lband'] = dc_indicator.donchian_channel_lband()
            volatility_indicators['donchian_channel_mband'] = dc_indicator.donchian_channel_mband()
            volatility_indicators['donchian_channel_pband'] = dc_indicator.donchian_channel_pband()
            volatility_indicators['donchian_channel_wband'] = dc_indicator.donchian_channel_wband()
            
            # Ulcer Index (UI)
            volatility_indicators['ulcer_index'] = ta.volatility.UlcerIndex(close=close).ulcer_index()
            
        except Exception as e:
            print(f"Error calculating volatility indicators: {e}")
        
        return volatility_indicators
    
    def calculate_trend_indicators(self):
        """Calculate all trend-based technical indicators"""
        high = self.df['High']
        low = self.df['Low']
        close = self.df['Adj Close']
        volume = self.df['Volume']
        
        trend_indicators = {}
        
        try:
            # Simple Moving Average (SMA)
            trend_indicators['sma_indicator'] = ta.trend.SMAIndicator(close=close, window=20).sma_indicator()
            
            # Exponential Moving Average (EMA)
            trend_indicators['ema_indicator'] = ta.trend.EMAIndicator(close=close).ema_indicator()
            
            # Weighted Moving Average (WMA)
            trend_indicators['wma_indicator'] = ta.trend.WMAIndicator(close=close, window=14).wma()
            
            # Moving Average Convergence Divergence (MACD)
            macd_indicator = ta.trend.MACD(close=close)
            trend_indicators['macd'] = macd_indicator.macd()
            trend_indicators['macd_diff'] = macd_indicator.macd_diff()
            trend_indicators['macd_signal'] = macd_indicator.macd_signal()
            
            # Average Directional Movement Index (ADX)
            adx_indicator = ta.trend.ADXIndicator(high=high, low=low, close=close)
            trend_indicators['adx'] = adx_indicator.adx()
            trend_indicators['adx_neg'] = adx_indicator.adx_neg()
            trend_indicators['adx_pos'] = adx_indicator.adx_pos()
            
            # Vortex Indicator (VI)
            vi_indicator = ta.trend.VortexIndicator(high=high, low=low, close=close)
            trend_indicators['vortex_indicator_neg'] = vi_indicator.vortex_indicator_neg()
            trend_indicators['vortex_indicator_pos'] = vi_indicator.vortex_indicator_pos()
            
            # Trix (TRIX)
            trend_indicators['trix'] = ta.trend.TRIXIndicator(close=close).trix()
            
            # Mass Index (MI)
            trend_indicators['mass_index'] = ta.trend.MassIndex(high=high, low=low, window_fast=9, window_slow=25).mass_index()
            
            # Commodity Channel Index (CCI)
            trend_indicators['cci'] = ta.trend.CCIIndicator(high=high, low=low, close=close).cci()
            
            # Detrended Price Oscillator (DPO)
            trend_indicators['dpo'] = ta.trend.DPOIndicator(close=close).dpo()
            
            # KST Oscillator (KST)
            kst_indicator = ta.trend.KSTIndicator(close=close)
            trend_indicators['kst'] = kst_indicator.kst()
            trend_indicators['kst_sig'] = kst_indicator.kst_sig()
            
            # Ichimoku Kinkō Hyō (Ichimoku)
            ichimoku_indicator = ta.trend.IchimokuIndicator(high=high, low=low, window1=9, window2=26, window3=52)
            trend_indicators['ichimoku_a'] = ichimoku_indicator.ichimoku_a()
            trend_indicators['ichimoku_b'] = ichimoku_indicator.ichimoku_b()
            trend_indicators['ichimoku_base_line'] = ichimoku_indicator.ichimoku_base_line()
            trend_indicators['ichimoku_conversion_line'] = ichimoku_indicator.ichimoku_conversion_line()
            
            # Parabolic Stop And Reverse (Parabolic SAR)
            psar_indicator = ta.trend.PSARIndicator(high=high, low=low, close=close)
            trend_indicators['psar_down'] = psar_indicator.psar_down()
            trend_indicators['psar_down_indicator'] = psar_indicator.psar_down_indicator()
            trend_indicators['psar_up'] = psar_indicator.psar_up()
            trend_indicators['psar_up_indicator'] = psar_indicator.psar_up_indicator()
            
            # Schaff Trend Cycle (STC)
            trend_indicators['stc'] = ta.trend.STCIndicator(close=close).stc()
            
            # Aroon Indicator
            aroon_indicator = ta.trend.AroonIndicator(high=high, low=low)
            trend_indicators['aroon_down'] = aroon_indicator.aroon_down()
            trend_indicators['aroon_up'] = aroon_indicator.aroon_up()
            
        except Exception as e:
            print(f"Error calculating trend indicators: {e}")
        
        return trend_indicators

    def calculate_momentum_indicators(self):
        """Calculate all momentum-based technical indicators"""
        high = self.df['High']
        low = self.df['Low']
        close = self.df['Adj Close']
        volume = self.df['Volume']

        momentum_indicators = {}

        try:
            # Relative Strength Index (RSI)
            momentum_indicators['rsi'] = ta.momentum.RSIIndicator(close=close).rsi()

            # Stochastic RSI (SRSI)
            stochrsi_indicator = ta.momentum.StochRSIIndicator(close=close)
            momentum_indicators['stochrsi'] = stochrsi_indicator.stochrsi()
            momentum_indicators['stochrsi_d'] = stochrsi_indicator.stochrsi_d()
            momentum_indicators['stochrsi_k'] = stochrsi_indicator.stochrsi_k()

            # True strength index (TSI)
            momentum_indicators['tsi'] = ta.momentum.TSIIndicator(close=close).tsi()

            # Ultimate Oscillator (UO)
            momentum_indicators['ultimate_oscillator'] = ta.momentum.UltimateOscillator(
                high=high, low=low, close=close
            ).ultimate_oscillator()

            # Stochastic Oscillator (SR)
            stoch_indicator = ta.momentum.StochasticOscillator(high=high, low=low, close=close)
            momentum_indicators['stoch'] = stoch_indicator.stoch()
            momentum_indicators['stoch_signal'] = stoch_indicator.stoch_signal()

            # Williams %R (WR)
            momentum_indicators['williams_r'] = ta.momentum.WilliamsRIndicator(
                high=high, low=low, close=close
            ).williams_r()

            # Awesome Oscillator (AO)
            momentum_indicators['awesome_oscillator'] = ta.momentum.AwesomeOscillatorIndicator(
                high=high, low=low
            ).awesome_oscillator()

            # Kaufman's Adaptive Moving Average (KAMA)
            momentum_indicators['kama'] = ta.momentum.KAMAIndicator(close=close).kama()

            # Rate of Change (ROC)
            momentum_indicators['roc'] = ta.momentum.ROCIndicator(close=close).roc()

            # Percentage Price Oscillator (PPO)
            ppo_indicator = ta.momentum.PercentagePriceOscillator(close=close)
            momentum_indicators['ppo'] = ppo_indicator.ppo()
            momentum_indicators['ppo_hist'] = ppo_indicator.ppo_hist()
            momentum_indicators['ppo_signal'] = ppo_indicator.ppo_signal()

            # Percentage Volume Oscillator (PVO)
            pvo_indicator = ta.momentum.PercentageVolumeOscillator(volume=volume)
            momentum_indicators['pvo'] = pvo_indicator.pvo()
            momentum_indicators['pvo_hist'] = pvo_indicator.pvo_hist()
            momentum_indicators['pvo_signal'] = pvo_indicator.pvo_signal()

        except Exception as e:
            print(f"Error calculating momentum indicators: {e}")

        return momentum_indicators

    def calculate_other_indicators(self):
        """Calculate other technical indicators"""
        close = self.df['Adj Close']

        other_indicators = {}

        try:
            # Daily Return (DR)
            other_indicators['daily_return'] = ta.others.DailyReturnIndicator(close=close).daily_return()

            # Daily Log Return (DLR)
            other_indicators['daily_log_return'] = ta.others.DailyLogReturnIndicator(close=close).daily_log_return()

            # Cumulative Return (CR)
            other_indicators['cumulative_return'] = ta.others.CumulativeReturnIndicator(close=close).cumulative_return()

        except Exception as e:
            print(f"Error calculating other indicators: {e}")

        return other_indicators

    def calculate_all_indicators(self):
        """Calculate all technical indicators and return organized results"""
        indicators = {
            'volume': self.calculate_volume_indicators(),
            'volatility': self.calculate_volatility_indicators(),
            'trend': self.calculate_trend_indicators(),
            'momentum': self.calculate_momentum_indicators(),
            'others': self.calculate_other_indicators()
        }

        return indicators

    def get_candlestick_data(self):
        """Get candlestick data in a structured format"""
        candlesticks = []

        for index, row in self.df.iterrows():
            candlestick = {
                'timestamp': index.isoformat() if hasattr(index, 'isoformat') else str(index),
                'open': float(row['Open']),
                'high': float(row['High']),
                'low': float(row['Low']),
                'close': float(row['Adj Close']),
                'volume': float(row['Volume'])
            }
            candlesticks.append(candlestick)

        return candlesticks

    def get_indicators_by_name(self, indicator_names):
        """Get specific indicators by name"""
        all_indicators = self.calculate_all_indicators()

        if 'all' in indicator_names:
            return all_indicators

        # Create a flat mapping of all indicator names to their values
        flat_indicators = {}
        for category, indicators in all_indicators.items():
            for name, values in indicators.items():
                flat_indicators[name] = {
                    'category': category,
                    'values': values.tolist() if hasattr(values, 'tolist') else values
                }

        # Filter requested indicators
        requested_indicators = {}
        for name in indicator_names:
            if name in flat_indicators:
                requested_indicators[name] = flat_indicators[name]

        return requested_indicators

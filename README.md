# Financial Trading Analysis API [![API Status](https://img.shields.io/badge/API-Active-brightgreen)](http://localhost:5000/docs)

## ⚡ Quick Start (30 seconds)

```bash
# 1. <PERSON><PERSON> and start the API
git clone <repository-url>
cd automating-technical-analysis
./start_background.sh start

# 2. Test the API
curl -H "X-API-Key: test-api-key-12345" http://localhost:5000/health

# 3. Get crypto analysis (OKX)
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-api-key-12345" \
  -d '{"asset": "Cryptocurrency", "equity": "BTC", "market": "USDT", "exchange": "OKX", "interval": "1 Day", "risk": "Medium"}'
```

**🔗 API Documentation:** [http://localhost:5000/docs](http://localhost:5000/docs)

## 🚀 Overview

A Flask-based REST API that provides automated technical analysis and trading signals for various financial instruments including stocks, cryptocurrencies, forex, futures, and index funds. Built with machine learning models and designed for seamless integration with automation platforms like n8n.

## 🎯 Project Goal

Profitable trading involves extensive knowledge of technical analysis, which can be complex and expensive to access. This API democratizes trading analysis by providing:

- **Automated Technical Analysis** using machine learning models
- **Real-time Trading Signals** with confidence scores
- **Multi-asset Support** (stocks, crypto, forex, futures, indices)
- **Secure API Access** with authentication
- **n8n Integration Ready** for workflow automation

## ✨ Features

### 🔐 Secure API Access
- API key authentication via `X-API-Key` header
- Environment variable configuration
- Comprehensive error handling

### 📊 Multi-Asset Analysis
- **Stocks:** S&P 500, NASDAQ, Dow Jones, international indices
- **Cryptocurrency:** Bitcoin, Ethereum, major altcoins via Binance & OKX
  - **OKX SPOT:** 657 trading pairs (BTC-USDT, ETH-USD, etc.)
  - **OKX SWAP:** 246 perpetual contracts (BTC-USD-SWAP, ETH-USD-SWAP)
  - **OKX FUTURES:** 23 dated futures (BTC-USD-250725, ETH-USD-250801)
- **Forex:** Major currency pairs (EUR/USD, GBP/USD, etc.)
- **Futures & Commodities:** Gold, Oil, Agricultural products
- **Index Funds:** Global market indices

### 🤖 Machine Learning Predictions
- **Action Predictions:** Buy/Sell/Hold recommendations
- **Price Forecasting:** ML-powered price predictions
- **Confidence Scores:** Reliability metrics for each prediction
- **Risk-based Levels:** Support/resistance levels for different risk tolerances

### 🔄 Automation Ready
- **RESTful API** design for easy integration
- **JSON responses** with structured trading signals
- **n8n workflow examples** included
- **Health check endpoint** for monitoring

## 🛠️ Quick Start

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/your-username/trading-analysis-api.git
cd trading-analysis-api

# Option A: Automatic setup (Recommended)
chmod +x run_api.sh
./run_api.sh

# Option B: Manual setup
pip install -r requirements.txt
export API_KEY="your-secret-api-key-here"
python Trade.py
```

### 2. API Usage

```bash
# Cryptocurrency analysis - OKX Spot (Recommended)
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-api-key-12345" \
  -d '{
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "OKX",
    "interval": "1 Day",
    "risk": "Medium"
  }'

# OKX Perpetual Swaps (High leverage)
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-api-key-12345" \
  -d '{
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "SWAP-USD",
    "exchange": "OKX",
    "interval": "1 Hour",
    "risk": "High"
  }'

# Stock analysis (requires market hours)
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-api-key-12345" \
  -d '{
    "asset": "Stocks",
    "equity": "Apple",
    "market": "US Dow Jones",
    "interval": "1 Day",
    "risk": "Medium"
  }'
```

### 3. Test Data Sources

```bash
# Test all data sources
curl -X GET http://localhost:5000/test \
  -H "X-API-Key: test-api-key-12345"
```

### 4. Access Documentation

Visit `http://localhost:5000/docs` for complete API documentation with interactive examples.

## 📡 API Endpoints

### `POST /analyze`
Performs comprehensive technical analysis and returns trading signals.

**Parameters:**
- `asset` (string): Asset type - "Stocks", "Cryptocurrency", "Forex", etc.
- `equity` (string, required): Specific instrument (e.g., "AAPL", "BTCUSDT")
- `market` (string): Market/exchange context
- `interval` (string): Time frame - "1 Day", "1 Hour", etc.
- `risk` (string): Risk level - "Low", "Medium", "High"

**Response:**
```json
{
  "status": "success",
  "analysis": {
    "recommended_action": "Buy",
    "current_price": 185.92,
    "predicted_price": 188.45,
    "confidence_scores": {
      "action_confidence": 78.5,
      "price_confidence": 82.1
    },
    "trading_signal": {
      "action": "Buy",
      "target_price": 188.45,
      "stop_loss": 182.15,
      "take_profit": 189.70
    }
  }
}
```

### `GET /health`
Health check endpoint for monitoring API availability.

### `GET /docs`
Interactive API documentation with examples and integration guides.

## 🔄 n8n Integration

### HTTP Request Node Configuration
```json
{
  "method": "POST",
  "url": "http://your-domain.com/analyze",
  "headers": {
    "X-API-Key": "{{$env.TRADING_API_KEY}}"
  },
  "body": {
    "asset": "Stocks",
    "equity": "AAPL",
    "interval": "1 Hour",
    "risk": "Medium"
  }
}
```

### Conditional Logic Example
```javascript
// Execute trade only if confidence > 75%
return items[0].json.analysis.confidence_scores.action_confidence > 75;
```

### Trading Alert Message
```javascript
const analysis = items[0].json.analysis;
return `🚨 ${analysis.recommended_action} ${analysis.equity} at $${analysis.current_price}`;
```

## 🔄 Running in Background & System Restart

### Option 1: Using Screen (Simple)

```bash
# Start in background using screen
screen -S trading-api
./run_api.sh
# Press Ctrl+A, then D to detach

# Reattach to view logs
screen -r trading-api

# List all screen sessions
screen -ls

# Kill the session
screen -S trading-api -X quit
```

### Option 2: Using nohup (Basic Background)

```bash
# Start in background with nohup
nohup ./run_api.sh > trading-api.log 2>&1 &

# Check if running
ps aux | grep "python Trade.py"

# View logs
tail -f trading-api.log

# Stop the process
pkill -f "python Trade.py"
```

### Option 3: Systemd Service (Production - Auto-restart)

Create a systemd service file:

```bash
sudo nano /etc/systemd/system/trading-api.service
```

Add the following content:

```ini
[Unit]
Description=Trading Analysis API
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=your-username
Group=your-username
WorkingDirectory=/path/to/automating-technical-analysis
Environment=API_KEY=your-secret-api-key-here
Environment=PATH=/path/to/automating-technical-analysis/venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/path/to/automating-technical-analysis/venv/bin/python Trade.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=trading-api

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable auto-start on boot
sudo systemctl enable trading-api

# Start the service
sudo systemctl start trading-api

# Check status
sudo systemctl status trading-api

# View logs
sudo journalctl -u trading-api -f

# Stop the service
sudo systemctl stop trading-api

# Restart the service
sudo systemctl restart trading-api
```

### Option 4: PM2 Process Manager (Node.js ecosystem)

```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'trading-api',
    script: 'python',
    args: 'Trade.py',
    cwd: '/path/to/automating-technical-analysis',
    interpreter: '/path/to/automating-technical-analysis/venv/bin/python',
    env: {
      API_KEY: 'your-secret-api-key-here'
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup

# Monitor processes
pm2 monit

# View logs
pm2 logs trading-api

# Restart
pm2 restart trading-api

# Stop
pm2 stop trading-api
```

### Option 5: Docker with Auto-restart

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

ENV API_KEY=your-secret-key
CMD ["python", "Trade.py"]
```

```bash
# Build and run with auto-restart
docker build -t trading-api .
docker run -d \
  --name trading-api \
  --restart unless-stopped \
  -p 5000:5000 \
  -e API_KEY=your-secret-key \
  trading-api

# View logs
docker logs -f trading-api

# Stop container
docker stop trading-api

# Start container
docker start trading-api
```

### Option 6: Docker Compose (Recommended for Production)

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  trading-api:
    build: .
    container_name: trading-api
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - API_KEY=your-secret-api-key-here
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

```bash
# Start in background
docker-compose up -d

# View logs
docker-compose logs -f

# Stop
docker-compose down

# Restart
docker-compose restart
```

## 🔧 Process Management Commands

### Check if API is Running

```bash
# Check process
ps aux | grep "python Trade.py" | grep -v grep

# Check port
netstat -tlnp | grep :5000
# or
ss -tlnp | grep :5000

# Test API health
curl -s http://localhost:5000/health
```

### Auto-start Script for Cron

Create a monitoring script:

```bash
cat > check_api.sh << 'EOF'
#!/bin/bash
API_DIR="/path/to/automating-technical-analysis"
LOG_FILE="$API_DIR/api_monitor.log"

# Check if API is responding
if ! curl -s --max-time 10 http://localhost:5000/health > /dev/null; then
    echo "$(date): API not responding, restarting..." >> "$LOG_FILE"

    # Kill existing process
    pkill -f "python Trade.py"
    sleep 5

    # Start new process
    cd "$API_DIR"
    nohup ./run_api.sh > trading-api.log 2>&1 &

    echo "$(date): API restarted" >> "$LOG_FILE"
else
    echo "$(date): API is healthy" >> "$LOG_FILE"
fi
EOF

chmod +x check_api.sh
```

Add to crontab for monitoring every 5 minutes:

```bash
# Edit crontab
crontab -e

# Add this line
*/5 * * * * /path/to/check_api.sh
```

## 🐳 Docker Deployment

## 📁 Project Structure

```
├── Trade.py                 # Main Flask API application
├── app/
│   ├── data_sourcing.py     # Market data collection
│   ├── indicator_analysis.py # Technical indicators
│   ├── graph.py             # Visualization components
│   ├── model.py             # ML prediction models
│   └── scaling.py           # Data preprocessing
├── models/
│   ├── action_prediction_model.h5  # Action prediction model
│   └── price_prediction_model.h5   # Price prediction model
├── market_data/             # Market data files
│   ├── stocks.txt
│   ├── binance.txt
│   ├── forex.txt
│   └── futures.txt
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## 🔧 Configuration

### Environment Variables
```bash
API_KEY=your-secret-api-key-here    # Required for API authentication
PORT=5000                           # Optional: Server port (default: 5000)
```

### Supported Time Intervals
- **Stocks/Forex/Futures:** 5min, 15min, 30min, 1h, 1d, 1w
- **Cryptocurrency:** 1min, 3min, 5min, 15min, 30min, 1h, 6h, 12h, 1d, 1w

## 🛡️ Security & Best Practices

- **API Key Authentication:** All endpoints require valid API key
- **Rate Limiting:** Recommended 60 requests/minute maximum
- **Error Handling:** Comprehensive error responses with status codes
- **Input Validation:** All parameters validated before processing
- **Confidence Thresholds:** Set minimum confidence levels for automated trading

## 📊 Monitoring & Logging

### Health Monitoring

```bash
# Basic health check
curl -s http://localhost:5000/health

# Detailed monitoring script
cat > monitor_api.sh << 'EOF'
#!/bin/bash
echo "=== Trading API Status ==="
echo "Date: $(date)"
echo

# Check process
if pgrep -f "python Trade.py" > /dev/null; then
    echo "✅ Process: Running (PID: $(pgrep -f 'python Trade.py'))"
else
    echo "❌ Process: Not running"
fi

# Check port
if netstat -tlnp 2>/dev/null | grep :5000 > /dev/null; then
    echo "✅ Port 5000: Open"
else
    echo "❌ Port 5000: Closed"
fi

# Check API health
if curl -s --max-time 5 http://localhost:5000/health > /dev/null; then
    echo "✅ API Health: Responding"
    echo "Response: $(curl -s http://localhost:5000/health)"
else
    echo "❌ API Health: Not responding"
fi

# Memory usage
echo "Memory Usage: $(ps -o pid,ppid,cmd,%mem,%cpu --sort=-%mem -C python | head -2)"
EOF

chmod +x monitor_api.sh
./monitor_api.sh
```

### Log Management

```bash
# View real-time logs (systemd)
sudo journalctl -u trading-api -f

# View logs with timestamps
sudo journalctl -u trading-api --since "1 hour ago"

# View application logs (if using nohup)
tail -f trading-api.log

# Rotate logs to prevent disk space issues
cat > rotate_logs.sh << 'EOF'
#!/bin/bash
LOG_DIR="/path/to/automating-technical-analysis"
MAX_SIZE=100M

if [ -f "$LOG_DIR/trading-api.log" ]; then
    if [ $(stat -f%z "$LOG_DIR/trading-api.log" 2>/dev/null || stat -c%s "$LOG_DIR/trading-api.log") -gt 104857600 ]; then
        mv "$LOG_DIR/trading-api.log" "$LOG_DIR/trading-api.log.$(date +%Y%m%d_%H%M%S)"
        touch "$LOG_DIR/trading-api.log"
        echo "Log rotated at $(date)"
    fi
fi
EOF

# Add to crontab for daily log rotation
echo "0 2 * * * /path/to/rotate_logs.sh" | crontab -
```

### Performance Monitoring

```bash
# Monitor API performance
cat > performance_check.sh << 'EOF'
#!/bin/bash
echo "=== API Performance Check ==="

# Response time test
echo "Testing response times..."
for endpoint in health docs assets; do
    response_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:5000/$endpoint)
    echo "$endpoint: ${response_time}s"
done

# Load test (requires Apache Bench)
if command -v ab &> /dev/null; then
    echo "Running load test (10 concurrent, 100 requests)..."
    ab -n 100 -c 10 -H "X-API-Key: test-api-key-12345" http://localhost:5000/health
fi
EOF

chmod +x performance_check.sh
```

### Troubleshooting Common Issues

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Port Already in Use** | `Address already in use` error | `sudo lsof -i :5000` then `kill -9 <PID>` |
| **Permission Denied** | Cannot bind to port | Run with `sudo` or use port > 1024 |
| **Module Not Found** | Import errors on startup | Activate virtual environment: `source venv/bin/activate` |
| **API Key Invalid** | 401 Unauthorized responses | Check `API_KEY` environment variable |
| **No Market Data** | 503 Service Unavailable | Check internet connection, try cryptocurrency endpoints |
| **High Memory Usage** | System slowdown | Restart service: `sudo systemctl restart trading-api` |
| **SSL Certificate** | HTTPS connection errors | Use HTTP for local development |

### Automated Alerts

Set up email alerts for API downtime:

```bash
# Install mailutils
sudo apt-get install mailutils

# Create alert script
cat > api_alert.sh << 'EOF'
#!/bin/bash
EMAIL="<EMAIL>"
API_URL="http://localhost:5000/health"

if ! curl -s --max-time 10 "$API_URL" > /dev/null; then
    echo "Trading API is down at $(date)" | mail -s "🚨 Trading API Alert" "$EMAIL"

    # Try to restart (if using systemd)
    sudo systemctl restart trading-api
    sleep 30

    if curl -s --max-time 10 "$API_URL" > /dev/null; then
        echo "Trading API has been restarted successfully at $(date)" | mail -s "✅ Trading API Recovered" "$EMAIL"
    fi
fi
EOF

# Add to crontab for every 5 minutes
echo "*/5 * * * * /path/to/api_alert.sh" | crontab -
```

### Built-in Features

- **Health Check:** `/health` endpoint for uptime monitoring
- **Error Tracking:** Detailed error messages in responses
- **Performance:** Built-in garbage collection for memory management
- **Debug Endpoint:** `/debug` for troubleshooting data issues
- **Test Endpoint:** `/test` for verifying data source connectivity

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This API provides educational and informational trading signals based on technical analysis and machine learning models. **This is not financial advice.** Always:

- Conduct your own research
- Consider consulting with financial advisors
- Understand that past performance doesn't guarantee future results
- Use proper risk management in all trading activities
- Start with small amounts when testing automated strategies

## 🆘 Support

- **Documentation:** [http://your-domain.com/docs](http://your-domain.com/docs)
- **Issues:** [GitHub Issues](https://github.com/your-username/trading-analysis-api/issues)
- **Discussions:** [GitHub Discussions](https://github.com/your-username/trading-analysis-api/discussions)

## 🚀 Quick Reference

### Start API (Production)
```bash
# Systemd (recommended)
sudo systemctl start trading-api

# Docker Compose
docker-compose up -d

# Screen session
screen -S trading-api
./run_api.sh
```

### Check Status
```bash
# Health check
curl -s http://localhost:5000/health

# Process status
sudo systemctl status trading-api

# View logs
sudo journalctl -u trading-api -f
```

### Test API
```bash
# Test data sources
curl -X GET http://localhost:5000/test -H "X-API-Key: test-api-key-12345"

# Crypto analysis
curl -X POST http://localhost:5000/analyze \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-api-key-12345" \
  -d '{"asset": "Cryptocurrency", "equity": "BTC", "market": "USDT", "interval": "1 Day", "risk": "Medium"}'
```

### Emergency Restart
```bash
# Kill all processes
pkill -f "python Trade.py"

# Restart service
sudo systemctl restart trading-api

# Or manual restart
./run_api.sh
```

---

## Built with ❤️ using Flask, TensorFlow, and Machine Learning

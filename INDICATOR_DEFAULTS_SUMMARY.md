# Technical Indicators Default Parameters Summary

## 📊 Updated Documentation

The `NEW_INDICATORS_ENDPOINT.md` and `INDICATORS.md` files have been updated to include comprehensive default parameter information for all 80+ technical indicators.

## 🎯 Key Default Parameters

### Volume Indicators
| Indicator | Default Parameters | Notes |
|-----------|-------------------|-------|
| Money Flow Index (MFI) | window=14 | Standard RSI-like period |
| Chaikin Money Flow (CMF) | window=20 | Standard period for money flow |
| Force Index (FI) | window=13 | Standard force calculation period |
| Ease of Movement (EoM) | window=14 | Standard smoothing period |

### Volatility Indicators
| Indicator | Default Parameters | Notes |
|-----------|-------------------|-------|
| Average True Range (ATR) | window=14 | Industry standard |
| Bollinger Bands | window=20, std_dev=2 | Most common settings |
| Keltner Channel | window=20, atr_window=10, multiplier=2 | Standard volatility channel |
| Donchian Channel | window=20, offset=0 | Standard breakout period |

### Trend Indicators
| Indicator | Default Parameters | Notes |
|-----------|-------------------|-------|
| RSI | window=14 | Industry standard |
| MACD | fast=12, slow=26, signal=9 | Classic MACD settings |
| **SMA** | **window=20** | **Custom: Set to 20 for daily analysis** |
| EMA | window=14 | Standard exponential smoothing |
| **WMA** | **window=14** | **Custom: Explicitly set** |
| ADX | window=14 | Standard directional movement |
| **Mass Index** | **fast=9, slow=25** | **Custom: Better sensitivity** |
| CCI | window=20, constant=0.015 | Standard commodity channel |
| **Ichimoku** | **conv=9, base=26, span=52** | **Custom: Traditional Japanese settings** |
| Parabolic SAR | step=0.02, max_step=0.2 | Standard SAR parameters |
| Aroon | window=25 | Standard Aroon period |

### Momentum Indicators
| Indicator | Default Parameters | Notes |
|-----------|-------------------|-------|
| Stochastic | window=14, smooth=3 | Standard stochastic settings |
| Williams %R | lookback=14 | Standard Williams period |
| Ultimate Oscillator | short=7, medium=14, long=28 | Multi-timeframe standard |
| KAMA | window=10, pow1=2, pow2=30 | Adaptive moving average |
| ROC | window=12 | Standard rate of change |

## 🔧 Custom Parameters (Highlighted in Bold)

The following indicators use **custom parameters** that differ from ta library defaults:

1. **Simple Moving Average (SMA)**
   - **Default: window=20** (instead of ta library default)
   - **Reason**: 20 periods is more common for daily analysis

2. **Weighted Moving Average (WMA)**
   - **Default: window=14** (explicitly set)
   - **Reason**: Consistency with other 14-period indicators

3. **Mass Index**
   - **Default: window_fast=9, window_slow=25** (custom settings)
   - **Reason**: Better sensitivity for trend reversal detection

4. **Ichimoku Kinkō Hyō**
   - **Default: window1=9, window2=26, window3=52** (traditional settings)
   - **Reason**: Standard Japanese Ichimoku parameters

## 📈 Industry Standard Parameters

Most indicators use **industry-standard parameters** that are:
- ✅ Used by major trading platforms (TradingView, MetaTrader, etc.)
- ✅ Recommended in technical analysis literature
- ✅ Optimized for general-purpose analysis
- ✅ Compatible with the ta library defaults

## 🎯 Parameter Categories

### 14-Period Indicators (Most Common)
- RSI (window=14)
- ATR (window=14)
- ADX (window=14)
- EMA (window=14)
- WMA (window=14)
- Stochastic (window=14)
- Williams %R (lbp=14)
- Ulcer Index (window=14)

### 20-Period Indicators
- SMA (window=20) - Custom
- Bollinger Bands (window=20)
- Keltner Channel (window=20)
- Donchian Channel (window=20)
- CCI (window=20)
- DPO (window=20)
- CMF (window=20)

### MACD Family (12, 26, 9)
- MACD (fast=12, slow=26, signal=9)
- PPO (fast=12, slow=26, signal=9)
- PVO (fast=12, slow=26, signal=9)

### Multi-Period Indicators
- Ultimate Oscillator (7, 14, 28)
- Ichimoku (9, 26, 52) - Custom
- Mass Index (9, 25) - Custom

## 🚀 Usage Examples

### Get Indicators with Default Parameters
```bash
curl -X POST http://localhost:5000/indicators/compact \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test-api-key-12345" \
  -d '{
    "equity": "BTC",
    "indicatorName": "rsi,macd,sma_indicator,bollinger_hband"
  }'
```

**Returns:**
- RSI with 14-period window
- MACD with 12/26/9 parameters
- SMA with 20-period window (custom)
- Bollinger upper band with 20-period window, 2 std dev

### Popular Indicator Combinations
```bash
# Day Trading Setup (shorter periods)
"indicatorName": "rsi,macd,bollinger_hband,bollinger_lband,ema_indicator"

# Swing Trading Setup (standard periods)
"indicatorName": "sma_indicator,ema_indicator,macd,rsi,average_true_range"

# Volume Analysis Setup
"indicatorName": "on_balance_volume,money_flow_index,chaikin_money_flow"
```

## 📚 Documentation Files Updated

1. **`NEW_INDICATORS_ENDPOINT.md`**
   - Added comprehensive default parameters table
   - Added parameter customization notes
   - Added future enhancement section

2. **`INDICATORS.md`**
   - Updated all indicator tables with default parameters
   - Added parameter notes section
   - Highlighted custom parameters in bold

3. **`INDICATOR_DEFAULTS_SUMMARY.md`** (this file)
   - Complete summary of all default parameters
   - Categorized by indicator type and period
   - Usage examples and best practices

## 🎉 Benefits

✅ **Complete Transparency**: Users know exactly what parameters are used
✅ **Industry Standards**: Most parameters follow trading platform conventions
✅ **Custom Optimizations**: Key indicators use optimized settings
✅ **Easy Reference**: Quick lookup for all 80+ indicators
✅ **Future Ready**: Framework for parameter customization

The documentation now provides complete visibility into all indicator calculations and their default parameters!

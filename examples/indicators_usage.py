#!/usr/bin/env python3
"""
Example usage of the /indicators endpoint
Demonstrates how to get candlestick data with technical indicators
"""

import requests
import json
import pandas as pd
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
API_KEY = "test-api-key-12345"

class TradingIndicatorsClient:
    """Client for the Trading Analysis API indicators endpoint"""
    
    def __init__(self, base_url=BASE_URL, api_key=API_KEY):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "X-API-Key": api_key
        }
    
    def get_indicators(self, asset="Cryptocurrency", equity="BTC", market="USDT", 
                      exchange="Binance", bar="1H", limit=100, indicator_name="all"):
        """
        Get candlestick data with technical indicators
        
        Args:
            asset: Asset type (Cryptocurrency, Stocks, etc.)
            equity: Specific instrument (BTC, AAPL, etc.)
            market: Market type (USDT, S&P 500, etc.)
            exchange: Exchange (Binance, OKX for crypto)
            bar: Time interval (1m, 5m, 1H, 1D, etc.)
            limit: Number of candlesticks (max 100)
            indicator_name: Specific indicators or "all"
        
        Returns:
            dict: API response with candlesticks and indicators
        """
        
        payload = {
            "asset": asset,
            "equity": equity,
            "market": market,
            "exchange": exchange,
            "bar": bar,
            "limit": limit,
            "indicatorName": indicator_name
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/indicators",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                error_data = response.json() if response.headers.get('content-type') == 'application/json' else {}
                raise Exception(f"API Error {response.status_code}: {error_data.get('error', response.text)}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {e}")
    
    def to_dataframe(self, api_response):
        """
        Convert API response to pandas DataFrame for analysis
        
        Args:
            api_response: Response from get_indicators()
        
        Returns:
            pd.DataFrame: DataFrame with candlesticks and indicators
        """
        
        if api_response['status'] != 'success':
            raise ValueError("API response indicates failure")
        
        data = api_response['data']
        
        # Convert candlesticks to DataFrame
        df = pd.DataFrame(data['candlesticks'])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        # Add indicators to DataFrame
        if 'indicators' in data:
            for category, indicators in data['indicators'].items():
                for name, values in indicators.items():
                    # Handle different value types
                    if isinstance(values, list) and len(values) == len(df):
                        df[f"{category}_{name}"] = values
                    elif hasattr(values, '__len__') and len(values) == len(df):
                        df[f"{category}_{name}"] = list(values)
        
        return df

def example_crypto_analysis():
    """Example: Get BTC indicators from Binance"""
    print("📊 Example 1: BTC Technical Analysis (Binance)")
    print("=" * 50)
    
    client = TradingIndicatorsClient()
    
    try:
        # Get BTC data with all indicators
        response = client.get_indicators(
            asset="Cryptocurrency",
            equity="BTC",
            market="USDT",
            exchange="Binance",
            bar="1H",
            limit=50,
            indicator_name="all"
        )
        
        print(f"✅ Retrieved data for {response['data']['equity']}")
        print(f"Exchange: {response['data']['exchange']}")
        print(f"Interval: {response['data']['interval']}")
        print(f"Candlesticks: {len(response['data']['candlesticks'])}")
        
        # Show indicator categories
        if 'indicators' in response['data']:
            for category, indicators in response['data']['indicators'].items():
                print(f"{category.title()}: {len(indicators)} indicators")
        
        # Convert to DataFrame for analysis
        df = client.to_dataframe(response)
        print(f"\nDataFrame shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Show latest values
        print(f"\nLatest Close Price: ${df['close'].iloc[-1]:,.2f}")
        if 'momentum_rsi' in df.columns:
            print(f"Latest RSI: {df['momentum_rsi'].iloc[-1]:.2f}")
        if 'trend_sma_indicator' in df.columns:
            print(f"Latest SMA: ${df['trend_sma_indicator'].iloc[-1]:,.2f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def example_specific_indicators():
    """Example: Get specific indicators only"""
    print("\n📈 Example 2: Specific Indicators (ETH)")
    print("=" * 50)
    
    client = TradingIndicatorsClient()
    
    try:
        # Get only RSI, MACD, and Bollinger Bands
        response = client.get_indicators(
            asset="Cryptocurrency",
            equity="ETH",
            market="USDT",
            exchange="OKX",
            bar="5m",
            limit=30,
            indicator_name="rsi,macd,bollinger_hband,bollinger_lband,sma_indicator"
        )
        
        print(f"✅ Retrieved specific indicators for {response['data']['equity']}")
        
        # Show requested indicators
        if 'indicators' in response['data']:
            print("Requested indicators:")
            for name, data in response['data']['indicators'].items():
                print(f"  - {name}: {data['category']}")
        
        return response
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def example_stock_analysis():
    """Example: Stock analysis with indicators"""
    print("\n📊 Example 3: Stock Analysis (AAPL)")
    print("=" * 50)
    
    client = TradingIndicatorsClient()
    
    try:
        # Get AAPL data with all indicators
        response = client.get_indicators(
            asset="Stocks",
            equity="AAPL",
            market="S&P 500",
            bar="1D",
            limit=100,
            indicator_name="all"
        )
        
        print(f"✅ Retrieved data for {response['data']['equity']}")
        print(f"Market: {response['data']['market']}")
        print(f"Interval: {response['data']['interval']}")
        
        # Convert to DataFrame
        df = client.to_dataframe(response)
        print(f"DataFrame shape: {df.shape}")
        
        # Show some analysis
        print(f"\nLatest Close Price: ${df['close'].iloc[-1]:,.2f}")
        print(f"30-day High: ${df['high'].tail(30).max():,.2f}")
        print(f"30-day Low: ${df['low'].tail(30).min():,.2f}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Trading Indicators API Examples")
    print("=" * 60)
    
    # Run examples
    btc_df = example_crypto_analysis()
    eth_response = example_specific_indicators()
    aapl_df = example_stock_analysis()
    
    print("\n" + "=" * 60)
    print("✨ Examples completed!")
    print("\n💡 Tips:")
    print("- Use 'all' for indicatorName to get all indicators")
    print("- Specify comma-separated indicator names for specific ones")
    print("- Maximum limit is 100 candlesticks per request")
    print("- Supported intervals: 1m, 3m, 5m, 15m, 30m, 1H, 2H, 4H, 1D")
    print("- Convert response to DataFrame for easy analysis")

# n8n Integration Guide for Trading Indicators API

## 🚨 Issue Resolution

**Problem**: The original `/indicators` endpoint with `"indicatorName": "all"` returns large responses (~105KB) that can cause n8n to throw "Invalid JSON in response body" errors.

**Solution**: We've created two solutions:

## 🎯 Solution 1: Compact Endpoint (Recommended for n8n)

### New Endpoint: `/indicators/compact`

This endpoint is specifically optimized for n8n and automation workflows:

- ✅ **Smaller Response Size**: ~2-3KB instead of 105KB
- ✅ **Latest Values Only**: Returns only the most recent indicator values
- ✅ **Popular Indicators**: Focuses on the most commonly used indicators
- ✅ **Clean JSON**: No NaN values, all nulls are properly handled
- ✅ **n8n Compatible**: Tested to work perfectly with n8n workflows

### Usage in n8n

**HTTP Request Node Configuration:**

```json
{
  "method": "POST",
  "url": "http://localhost:5000/indicators/compact",
  "headers": {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-12345"
  },
  "body": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "bar": "1D",
    "limit": 30,
    "indicatorName": "all"
  }
}
```

### Compact Response Format

```json
{
  "status": "success",
  "data": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "exchange": "Binance",
    "interval": "1D",
    "latest_candle": {
      "close": 118696.37,
      "high": 119573.53,
      "low": 116128.0,
      "open": 117380.36,
      "timestamp": "2025-07-22T03:00:00",
      "volume": 16581.32166
    },
    "indicators": {
      "rsi": {
        "category": "momentum",
        "latest_value": 68.98,
        "values_count": 30
      },
      "macd": {
        "category": "trend",
        "latest_value": 2808.81,
        "values_count": 30
      }
    },
    "latest_indicator_values": {
      "rsi": 68.98,
      "macd": 2808.81
    }
  },
  "metadata": {
    "indicators_count": 10,
    "response_type": "compact"
  }
}
```

## 🔧 Solution 2: Optimized Original Endpoint

The original `/indicators` endpoint has been improved:

- ✅ **NaN Handling**: All NaN values converted to `null`
- ✅ **Better JSON**: More reliable JSON parsing
- ✅ **Smaller Limits**: Use smaller `limit` values for n8n

### Usage for Specific Indicators

Instead of requesting "all" indicators, request specific ones:

```json
{
  "asset": "Cryptocurrency",
  "equity": "BTC",
  "market": "USDT",
  "exchange": "Binance",
  "bar": "1H",
  "limit": 20,
  "indicatorName": "rsi,macd,sma_indicator,bollinger_hband,bollinger_lband"
}
```

## 📊 n8n Workflow Examples

### Example 1: Simple BTC Analysis

```json
{
  "nodes": [
    {
      "name": "Get BTC Indicators",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:5000/indicators/compact",
        "headers": {
          "Content-Type": "application/json",
          "X-API-Key": "test-api-key-12345"
        },
        "body": {
          "asset": "Cryptocurrency",
          "equity": "BTC",
          "market": "USDT",
          "bar": "1H",
          "indicatorName": "all"
        }
      }
    }
  ]
}
```

### Example 2: Multiple Cryptocurrencies

```json
{
  "nodes": [
    {
      "name": "Crypto List",
      "type": "n8n-nodes-base.set",
      "parameters": {
        "values": {
          "string": [
            {"key": "crypto", "value": "BTC"},
            {"key": "crypto", "value": "ETH"},
            {"key": "crypto", "value": "ADA"}
          ]
        }
      }
    },
    {
      "name": "Get Indicators",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:5000/indicators/compact",
        "body": {
          "asset": "Cryptocurrency",
          "equity": "={{$json.crypto}}",
          "market": "USDT",
          "bar": "1D",
          "indicatorName": "rsi,macd,sma_indicator"
        }
      }
    }
  ]
}
```

## 🎯 Best Practices for n8n

### 1. Use Compact Endpoint
- Always use `/indicators/compact` for n8n workflows
- It's specifically designed for automation tools

### 2. Limit Data Size
- Use `limit: 20-30` for most use cases
- Larger limits may cause timeouts or memory issues

### 3. Request Specific Indicators
- Instead of "all", specify: `"rsi,macd,sma_indicator,bollinger_hband"`
- This reduces response size and processing time

### 4. Handle Null Values
- Some indicators may return `null` for recent periods
- Use n8n's conditional logic to handle these cases

### 5. Error Handling
```json
{
  "continueOnFail": true,
  "retryOnFail": true,
  "maxTries": 3
}
```

## 📈 Popular Indicator Combinations

### For Day Trading
```json
{
  "indicatorName": "rsi,macd,bollinger_hband,bollinger_lband,ema_indicator"
}
```

### For Swing Trading
```json
{
  "indicatorName": "sma_indicator,ema_indicator,macd,rsi,average_true_range"
}
```

### For Volume Analysis
```json
{
  "indicatorName": "on_balance_volume,money_flow_index,chaikin_money_flow"
}
```

## 🔍 Accessing Data in n8n

### Get Latest Price
```javascript
{{$json.data.latest_candle.close}}
```

### Get RSI Value
```javascript
{{$json.data.latest_indicator_values.rsi}}
```

### Check if RSI is Oversold
```javascript
{{$json.data.latest_indicator_values.rsi < 30}}
```

### Get All Indicator Values
```javascript
{{$json.data.latest_indicator_values}}
```

## 🚀 Complete n8n Workflow Example

```json
{
  "name": "Crypto Trading Signals",
  "nodes": [
    {
      "name": "Schedule",
      "type": "n8n-nodes-base.cron",
      "parameters": {
        "triggerTimes": {
          "hour": "*",
          "minute": "0"
        }
      }
    },
    {
      "name": "Get BTC Data",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:5000/indicators/compact",
        "headers": {
          "X-API-Key": "test-api-key-12345"
        },
        "body": {
          "asset": "Cryptocurrency",
          "equity": "BTC",
          "market": "USDT",
          "bar": "1H",
          "indicatorName": "rsi,macd,bollinger_hband,bollinger_lband"
        }
      }
    },
    {
      "name": "Check Signals",
      "type": "n8n-nodes-base.if",
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.data.latest_indicator_values.rsi}}",
              "operation": "smaller",
              "value2": "30"
            }
          ]
        }
      }
    },
    {
      "name": "Send Alert",
      "type": "n8n-nodes-base.slack",
      "parameters": {
        "text": "🚨 BTC RSI is oversold: {{$json.data.latest_indicator_values.rsi}}"
      }
    }
  ]
}
```

## 🎉 Summary

- ✅ Use `/indicators/compact` for n8n workflows
- ✅ Request specific indicators instead of "all"
- ✅ Use reasonable limits (20-30 candlesticks)
- ✅ Handle null values in your workflow logic
- ✅ The API now properly handles NaN values and large responses

This should resolve all n8n integration issues and provide a smooth automation experience!

#!/usr/bin/env python3
"""
Test script for the new /indicators endpoint
"""

import requests
import json
import os

# Configuration
BASE_URL = "http://localhost:5000"
API_KEY = "test-api-key-12345"

def test_indicators_endpoint():
    """Test the new indicators endpoint with various scenarios"""
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    # Test cases
    test_cases = [
        {
            "name": "BTC All Indicators (Binance)",
            "data": {
                "asset": "Cryptocurrency",
                "equity": "BTC",
                "market": "USDT",
                "exchange": "Binance",
                "bar": "1H",
                "limit": 50,
                "indicatorName": "all"
            }
        },
        {
            "name": "ETH Specific Indicators (OKX)",
            "data": {
                "asset": "Cryptocurrency",
                "equity": "ETH",
                "market": "USDT",
                "exchange": "OKX",
                "bar": "5m",
                "limit": 30,
                "indicatorName": "rsi,macd,bollinger_hband,sma_indicator"
            }
        },
        {
            "name": "Stock Indicators (AAPL)",
            "data": {
                "asset": "Stocks",
                "equity": "AAPL",
                "market": "S&P 500",
                "bar": "1D",
                "limit": 100,
                "indicatorName": "all"
            }
        }
    ]
    
    print("🧪 Testing /indicators endpoint...")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        try:
            # Make POST request
            response = requests.post(
                f"{BASE_URL}/indicators",
                headers=headers,
                json=test_case['data'],
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Success!")
                print(f"Asset: {data['data']['asset']}")
                print(f"Equity: {data['data']['equity']}")
                print(f"Exchange: {data['data']['exchange']}")
                print(f"Interval: {data['data']['interval']}")
                print(f"Candlesticks: {len(data['data']['candlesticks'])}")
                
                # Count indicators
                if 'indicators' in data['data']:
                    total_indicators = 0
                    for category, indicators in data['data']['indicators'].items():
                        count = len(indicators)
                        total_indicators += count
                        print(f"{category.title()} indicators: {count}")
                    print(f"Total indicators: {total_indicators}")
                
                print(f"Total records: {data['metadata']['total_records']}")
                
            else:
                print("❌ Failed!")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data.get('error', 'Unknown error')}")
                    if 'message' in error_data:
                        print(f"Message: {error_data['message']}")
                except:
                    print(f"Raw response: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")

def test_health_endpoint():
    """Test the health endpoint to ensure API is running"""
    try:
        response = requests.get(f"{BASE_URL}/health", headers={"X-API-Key": API_KEY})
        if response.status_code == 200:
            print("✅ API is healthy and running")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting API tests...")
    
    # First check if API is running
    if test_health_endpoint():
        test_indicators_endpoint()
    else:
        print("\n💡 Make sure the API is running:")
        print("   python Trade.py")
        print("   or")
        print("   ./start_background.sh start")

#!/bin/bash

# Quick API Status Check Script

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Trading Analysis API Status ===${NC}"
echo

# Check if process is running
if [ -f "trading-api.pid" ]; then
    PID=$(cat trading-api.pid)
    if ps -p "$PID" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Process Status: Running (PID: $PID)${NC}"
    else
        echo -e "${RED}❌ Process Status: Not running (stale PID file)${NC}"
        rm -f trading-api.pid
    fi
else
    echo -e "${RED}❌ Process Status: Not running${NC}"
fi

# Check if API is responding
echo -n "🌐 API Response: "
if curl -s --max-time 5 http://localhost:5000/health > /dev/null 2>&1; then
    echo -e "${GREEN}Responding${NC}"
    echo "   Health: $(curl -s http://localhost:5000/health)"
else
    echo -e "${RED}Not responding${NC}"
fi

# Check crontab
echo -n "⏰ Auto-start: "
if crontab -l 2>/dev/null | grep -q "auto_start_api.sh"; then
    echo -e "${GREEN}Configured (crontab)${NC}"
else
    echo -e "${RED}Not configured${NC}"
fi

# Show useful commands
echo
echo -e "${BLUE}=== Quick Commands ===${NC}"
echo "Start:   ./start_background.sh start"
echo "Stop:    ./start_background.sh stop"
echo "Restart: ./start_background.sh restart"
echo "Logs:    ./start_background.sh logs"
echo "Status:  ./start_background.sh status"
echo
echo "API URL: http://localhost:5000"
echo "Docs:    http://localhost:5000/docs"

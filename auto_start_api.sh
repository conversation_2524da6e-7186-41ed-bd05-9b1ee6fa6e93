#!/bin/bash

# Simple Auto-Start Script for Trading Analysis API
# This script can be added to crontab or system startup

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to the project directory
cd "$SCRIPT_DIR"

# Wait a bit for system to be ready (useful for boot startup)
sleep 10

# Check if API is already running
if [ -f "trading-api.pid" ]; then
    PID=$(cat trading-api.pid)
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "$(date): Trading API is already running (PID: $PID)"
        exit 0
    fi
fi

# Start the API
echo "$(date): Starting Trading Analysis API..."
./start_background.sh start

# Log the result
if [ $? -eq 0 ]; then
    echo "$(date): Trading API started successfully" >> startup.log
else
    echo "$(date): Failed to start Trading API" >> startup.log
fi

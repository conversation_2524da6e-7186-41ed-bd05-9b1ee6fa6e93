#!/bin/bash

# Trading Analysis API - Auto-Start Setup Script
# This script sets up the API to start automatically when the system boots

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get current directory and user
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURRENT_USER="$(whoami)"
SERVICE_NAME="trading-api"

echo "=============================================="
echo "Trading Analysis API - Auto-Start Setup"
echo "=============================================="
echo

print_status "Current configuration:"
echo "  User: $CURRENT_USER"
echo "  Project directory: $SCRIPT_DIR"
echo "  Service name: $SERVICE_NAME"
echo

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "Please do not run this script as root. Run as your regular user."
    exit 1
fi

# Function to show available options
show_options() {
    echo "Choose setup method:"
    echo "1) Install as systemd service (recommended - starts on boot)"
    echo "2) Add to user crontab (@reboot)"
    echo "3) Add to user's .bashrc/.profile"
    echo "4) Manual setup instructions only"
    echo "5) Exit"
    echo
}

# Function to install systemd service
install_systemd_service() {
    print_status "Installing systemd service..."
    
    # Check if template exists
    if [ ! -f "$SCRIPT_DIR/trading-api.service.template" ]; then
        print_error "Template file not found: $SCRIPT_DIR/trading-api.service.template"
        return 1
    fi
    
    # Get API key
    read -p "Enter your API key (or press Enter for default test key): " API_KEY
    if [ -z "$API_KEY" ]; then
        API_KEY="test-api-key-12345"
        print_warning "Using default test API key. Change this in production!"
    fi
    
    # Create service file
    SERVICE_FILE="/tmp/${SERVICE_NAME}.service"
    cp "$SCRIPT_DIR/trading-api.service.template" "$SERVICE_FILE"
    
    # Replace placeholders
    sed -i "s|REPLACE_WITH_YOUR_USERNAME|$CURRENT_USER|g" "$SERVICE_FILE"
    sed -i "s|REPLACE_WITH_FULL_PATH_TO_PROJECT|$SCRIPT_DIR|g" "$SERVICE_FILE"
    sed -i "s|REPLACE_WITH_YOUR_API_KEY|$API_KEY|g" "$SERVICE_FILE"
    
    # Remove comments for cleaner service file
    grep -v '^#' "$SERVICE_FILE" | grep -v '^$' > "${SERVICE_FILE}.clean"
    mv "${SERVICE_FILE}.clean" "$SERVICE_FILE"
    
    echo
    print_status "Service file created. Now run these commands:"
    echo
    echo "sudo cp $SERVICE_FILE /etc/systemd/system/${SERVICE_NAME}.service"
    echo "sudo chmod 644 /etc/systemd/system/${SERVICE_NAME}.service"
    echo "sudo systemctl daemon-reload"
    echo "sudo systemctl enable ${SERVICE_NAME}"
    echo "sudo systemctl start ${SERVICE_NAME}"
    echo
    print_status "To check status: sudo systemctl status ${SERVICE_NAME}"
    print_status "To view logs: sudo journalctl -u ${SERVICE_NAME} -f"
    
    # Clean up
    rm -f "$SERVICE_FILE"
}

# Function to add to crontab
install_crontab() {
    print_status "Adding to user crontab..."
    
    # Check if already exists
    if crontab -l 2>/dev/null | grep -q "$SCRIPT_DIR/start_background.sh"; then
        print_warning "Entry already exists in crontab"
        return 0
    fi
    
    # Add to crontab
    (crontab -l 2>/dev/null; echo "@reboot $SCRIPT_DIR/start_background.sh start") | crontab -
    
    if [ $? -eq 0 ]; then
        print_success "Added to crontab successfully"
        print_status "The API will start automatically on next reboot"
        print_status "To start now: $SCRIPT_DIR/start_background.sh start"
    else
        print_error "Failed to add to crontab"
        return 1
    fi
}

# Function to add to profile
install_profile() {
    print_status "Adding to user profile..."
    
    PROFILE_FILE="$HOME/.profile"
    START_COMMAND="$SCRIPT_DIR/start_background.sh start"
    
    # Check if already exists
    if grep -q "$START_COMMAND" "$PROFILE_FILE" 2>/dev/null; then
        print_warning "Entry already exists in $PROFILE_FILE"
        return 0
    fi
    
    # Add to profile
    echo "" >> "$PROFILE_FILE"
    echo "# Auto-start Trading Analysis API" >> "$PROFILE_FILE"
    echo "$START_COMMAND" >> "$PROFILE_FILE"
    
    print_success "Added to $PROFILE_FILE"
    print_status "The API will start when you log in"
    print_status "To start now: $START_COMMAND"
}

# Function to show manual instructions
show_manual_instructions() {
    print_status "Manual Setup Instructions:"
    echo
    echo "=== Option 1: Systemd Service (Recommended) ==="
    echo "1. Run: ./install_service.sh"
    echo "2. Follow the prompts"
    echo "3. The service will start automatically on boot"
    echo
    echo "=== Option 2: Crontab ==="
    echo "1. Run: crontab -e"
    echo "2. Add this line: @reboot $SCRIPT_DIR/start_background.sh start"
    echo "3. Save and exit"
    echo
    echo "=== Option 3: User Profile ==="
    echo "1. Edit ~/.profile or ~/.bashrc"
    echo "2. Add this line: $SCRIPT_DIR/start_background.sh start"
    echo "3. Save and exit"
    echo
    echo "=== Manual Control ==="
    echo "Start:   $SCRIPT_DIR/start_background.sh start"
    echo "Stop:    $SCRIPT_DIR/start_background.sh stop"
    echo "Status:  $SCRIPT_DIR/start_background.sh status"
    echo "Logs:    $SCRIPT_DIR/start_background.sh logs"
    echo
}

# Main menu
while true; do
    show_options
    read -p "Select option (1-5): " choice
    
    case $choice in
        1)
            install_systemd_service
            break
            ;;
        2)
            install_crontab
            break
            ;;
        3)
            install_profile
            break
            ;;
        4)
            show_manual_instructions
            break
            ;;
        5)
            print_status "Exiting..."
            exit 0
            ;;
        *)
            print_error "Invalid option. Please select 1-5."
            ;;
    esac
done

echo
print_success "Setup completed!"
print_status "Your Trading Analysis API is configured for auto-start."

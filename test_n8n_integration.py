#!/usr/bin/env python3
"""
Test script for n8n integration with the indicators endpoints
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5000"
API_KEY = "test-api-key-12345"

def test_compact_endpoint():
    """Test the compact endpoint optimized for n8n"""
    print("🧪 Testing /indicators/compact endpoint...")
    print("=" * 50)
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    test_cases = [
        {
            "name": "BTC Compact All Indicators",
            "data": {
                "asset": "Cryptocurrency",
                "equity": "BTC",
                "market": "USDT",
                "exchange": "Binance",
                "bar": "1D",
                "limit": 30,
                "indicatorName": "all"
            }
        },
        {
            "name": "ETH Compact Specific Indicators",
            "data": {
                "asset": "Cryptocurrency",
                "equity": "ETH",
                "market": "USDT",
                "exchange": "OKX",
                "bar": "1H",
                "limit": 20,
                "indicatorName": "rsi,macd,sma_indicator"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        try:
            response = requests.post(
                f"{BASE_URL}/indicators/compact",
                headers=headers,
                json=test_case['data'],
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Success!")
                print(f"Response Type: {data['metadata']['response_type']}")
                print(f"Asset: {data['data']['asset']}")
                print(f"Equity: {data['data']['equity']}")
                print(f"Exchange: {data['data']['exchange']}")
                print(f"Candlesticks Count: {data['data']['candlesticks_count']}")
                print(f"Indicators Count: {data['metadata']['indicators_count']}")
                
                # Check response size
                response_size = len(response.text)
                print(f"Response Size: {response_size:,} bytes ({response_size/1024:.1f} KB)")
                
                # Show latest values
                if 'latest_indicator_values' in data['data']:
                    latest_values = data['data']['latest_indicator_values']
                    print("Latest Indicator Values:")
                    for name, value in list(latest_values.items())[:3]:  # Show first 3
                        if value is not None:
                            print(f"  - {name}: {value:.2f}" if isinstance(value, (int, float)) else f"  - {name}: {value}")
                        else:
                            print(f"  - {name}: null")
                
                # Validate JSON structure for n8n compatibility
                required_fields = ['status', 'data', 'metadata']
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    print(f"⚠️  Missing required fields: {missing_fields}")
                else:
                    print("✅ JSON structure is n8n compatible")
                
            else:
                print("❌ Failed!")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"Raw response: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

def test_original_endpoint_optimized():
    """Test the original endpoint with optimizations"""
    print("\n🧪 Testing /indicators endpoint (optimized)...")
    print("=" * 50)
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    # Test with specific indicators to avoid large responses
    test_data = {
        "asset": "Cryptocurrency",
        "equity": "BTC",
        "market": "USDT",
        "exchange": "Binance",
        "bar": "1H",
        "limit": 15,
        "indicatorName": "rsi,macd,sma_indicator,bollinger_hband"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/indicators",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Success!")
            
            # Check response size
            response_size = len(response.text)
            print(f"Response Size: {response_size:,} bytes ({response_size/1024:.1f} KB)")
            
            # Check for NaN handling
            indicators = data['data']['indicators']
            nan_found = False
            null_count = 0
            
            for name, indicator_data in indicators.items():
                if 'values' in indicator_data:
                    values = indicator_data['values']
                    for value in values:
                        if value is None:
                            null_count += 1
                        elif isinstance(value, str) and value.lower() == 'nan':
                            nan_found = True
            
            if nan_found:
                print("⚠️  Found NaN values in response")
            else:
                print("✅ No NaN values found - properly converted to null")
            
            print(f"Null values count: {null_count}")
            print(f"Indicators returned: {len(indicators)}")
            
        else:
            print("❌ Failed!")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Raw response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error: {e}")

def test_n8n_workflow_simulation():
    """Simulate an n8n workflow using the compact endpoint"""
    print("\n🤖 Simulating n8n Workflow...")
    print("=" * 50)
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    # Simulate getting data for multiple cryptocurrencies
    cryptos = ["BTC", "ETH", "ADA"]
    results = []
    
    for crypto in cryptos:
        print(f"\nGetting data for {crypto}...")
        
        try:
            response = requests.post(
                f"{BASE_URL}/indicators/compact",
                headers=headers,
                json={
                    "asset": "Cryptocurrency",
                    "equity": crypto,
                    "market": "USDT",
                    "exchange": "Binance",
                    "bar": "1D",
                    "indicatorName": "rsi,macd"
                },
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                latest_values = data['data']['latest_indicator_values']
                
                result = {
                    "crypto": crypto,
                    "price": data['data']['latest_candle']['close'],
                    "rsi": latest_values.get('rsi'),
                    "macd": latest_values.get('macd')
                }
                results.append(result)
                print(f"✅ {crypto}: Price=${result['price']:,.2f}, RSI={result['rsi']:.2f if result['rsi'] else 'N/A'}")
                
            else:
                print(f"❌ Failed to get data for {crypto}")
                
        except Exception as e:
            print(f"❌ Error getting {crypto} data: {e}")
    
    # Simulate n8n logic - find oversold conditions
    print(f"\n📊 Analysis Results:")
    oversold_cryptos = []
    for result in results:
        if result['rsi'] and result['rsi'] < 35:
            oversold_cryptos.append(result)
    
    if oversold_cryptos:
        print("🚨 Oversold cryptocurrencies found:")
        for crypto in oversold_cryptos:
            print(f"  - {crypto['crypto']}: RSI {crypto['rsi']:.2f}")
    else:
        print("✅ No oversold conditions detected")
    
    return results

def main():
    """Run all tests"""
    print("🚀 n8n Integration Testing")
    print("=" * 60)
    
    # Test health endpoint first
    try:
        response = requests.get(f"{BASE_URL}/health", headers={"X-API-Key": API_KEY})
        if response.status_code == 200:
            print("✅ API is healthy and running\n")
        else:
            print("❌ API health check failed")
            return
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        print("\n💡 Make sure the API is running:")
        print("   export API_KEY='test-api-key-12345' && python3 Trade.py")
        return
    
    # Run tests
    test_compact_endpoint()
    test_original_endpoint_optimized()
    workflow_results = test_n8n_workflow_simulation()
    
    print("\n" + "=" * 60)
    print("🎉 Testing completed!")
    print("\n💡 For n8n integration:")
    print("✅ Use /indicators/compact endpoint")
    print("✅ Specify individual indicators instead of 'all'")
    print("✅ Use limit of 20-30 for best performance")
    print("✅ All responses are now n8n compatible")

if __name__ == "__main__":
    main()

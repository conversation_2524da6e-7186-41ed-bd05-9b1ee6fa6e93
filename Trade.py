from flask import Flask, request, jsonify, render_template_string
import os
import pandas as pd
from app.data_sourcing import Data_Sourcing, data_update
from app.indicator_analysis import Indications
from app.graph import Visualization
from app.comprehensive_indicators import ComprehensiveIndicators
from tensorflow.keras.models import load_model
import gc
import warnings

app = Flask(__name__)
warnings.filterwarnings("ignore")

# Load models globally
action_model = load_model("models/action_prediction_model.h5")
price_model = load_model("models/price_prediction_model.h5")
app_data = Data_Sourcing()

# HTML template for documentation
DOCS_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Analysis API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-size: 2.5em;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            font-size: 1.8em;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #7f8c8d;
            font-size: 1.3em;
            margin-top: 25px;
        }
        h4 {
            color: #5a6c7d;
            font-size: 1.1em;
            margin-top: 20px;
        }
        code {
            background: #f8f9fa;
            color: #e74c3c;
            padding: 3px 8px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
            font-size: 0.9em;
            border: 1px solid #e9ecef;
        }
        pre {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 25px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            border: 1px solid #444;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }
        pre code {
            background: transparent !important;
            color: #f8f8f2 !important;
            padding: 0 !important;
            border: none !important;
            font-size: 0.95em;
            line-height: 1.5;
        }
        pre::before {
            content: "💻";
            position: absolute;
            top: 8px;
            right: 12px;
            opacity: 0.6;
        }
        .endpoint {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
            padding: 20px;
            border-left: 5px solid #27ae60;
            margin: 25px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
        }
        .method {
            background: #3498db;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.85em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .method.post { background: linear-gradient(135deg, #e67e22, #d35400); }
        .method.get { background: linear-gradient(135deg, #27ae60, #229954); }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #e9ecef;
            padding: 15px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 600;
            color: #495057;
            text-transform: uppercase;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        tr:hover {
            background: #e3f2fd;
            transition: background 0.3s ease;
        }
        .error {
            background: linear-gradient(135deg, #fdf2f2, #fce4ec);
            border-left: 5px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .success {
            background: linear-gradient(135deg, #f0f9f0, #e8f5e8);
            border-left: 5px solid #27ae60;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .warning {
            background: linear-gradient(135deg, #fef9e7, #fff3cd);
            border-left: 5px solid #f39c12;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .nav {
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 30px;
            border-radius: 8px;
            text-align: center;
        }
        .nav a {
            margin: 0 15px;
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        .nav a:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
        }
        .badge {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin: 8px 0;
            color: #555;
        }
        footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            text-align: center;
            color: #7f8c8d;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <a href="#overview">Overview</a>
            <a href="#authentication">Authentication</a>
            <a href="#endpoints">Endpoints</a>
            <a href="#examples">Examples</a>
            <a href="#n8n">n8n Integration</a>
            <a href="#errors">Error Handling</a>
        </div>

        <h1 id="overview">🚀 Trading Analysis API Documentation</h1>
        
        <div class="success">
            <strong>API Status:</strong> <span class="badge">Active</span> | 
            <strong>Version:</strong> 1.0 | 
            <strong>Base URL:</strong> <code>{{ base_url }}</code>
        </div>

        <p>The Trading Analysis API provides automated technical analysis and trading signals for various financial instruments including stocks, cryptocurrencies, forex, futures, and index funds. Built with Flask and powered by machine learning models, this API delivers actionable trading recommendations with confidence scores.</p>

        <h2 id="authentication">🔐 Authentication</h2>
        <p>All API requests require authentication using an API key passed in the request header.</p>
        
        <table>
            <tr><th>Header</th><th>Value</th><th>Required</th></tr>
            <tr><td><code>Content-Type</code></td><td><code>application/json</code></td><td>✅ Yes</td></tr>
            <tr><td><code>X-API-Key</code></td><td>Your API key</td><td>✅ Yes</td></tr>
        </table>

        <h2 id="endpoints">📡 Endpoints</h2>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /analyze</h3>
            <p>Performs comprehensive technical analysis and returns trading signals with price predictions.</p>
        </div>

        <h4>Request Parameters</h4>
        <table>
            <tr><th>Parameter</th><th>Type</th><th>Required</th><th>Default</th><th>Description</th></tr>
            <tr><td><code>asset</code></td><td>string</td><td>No</td><td>"Stocks"</td><td>Asset type: "Stocks", "Cryptocurrency", "Forex", "Index Fund", "Futures & Commodities"</td></tr>
            <tr><td><code>equity</code></td><td>string</td><td>✅ Yes</td><td>-</td><td>Specific instrument (e.g., "AAPL", "BTC")</td></tr>
            <tr><td><code>market</code></td><td>string</td><td>No</td><td>Auto-detected</td><td>Market type (e.g., "S&P 500", "USDT", "SWAP-USD", "FUTURES-USD")</td></tr>
            <tr><td><code>exchange</code></td><td>string</td><td>No</td><td>"Binance"</td><td>Exchange for crypto: "Binance", "OKX" (only for Cryptocurrency asset type)</td></tr>
            <tr><td><code>interval</code></td><td>string</td><td>No</td><td>"1 Day"</td><td>Time interval for analysis</td></tr>
            <tr><td><code>risk</code></td><td>string</td><td>No</td><td>"Medium"</td><td>Risk level: "Low", "Medium", "High"</td></tr>
        </table>

        <h4>Supported Exchanges & Markets</h4>
        <table>
            <tr><th>Exchange</th><th>Market Types</th><th>Instruments</th><th>Description</th></tr>
            <tr><td><strong>Binance</strong></td><td>USDT, BTC, ETH, USD, BUSD</td><td>600+</td><td>Spot cryptocurrency trading</td></tr>
            <tr><td><strong>OKX</strong></td><td>USDT, USD, BTC, ETH, USDC</td><td>657</td><td>Spot cryptocurrency trading</td></tr>
            <tr><td><strong>OKX</strong></td><td>SWAP-USD</td><td>246</td><td>USD-margined perpetual swaps</td></tr>
            <tr><td><strong>OKX</strong></td><td>FUTURES-USD</td><td>23</td><td>USD-margined futures contracts</td></tr>
            <tr><td><strong>Yahoo Finance</strong></td><td>Various</td><td>5000+</td><td>Stocks, forex, futures, indices</td></tr>
        </table>

        <h4>Example Request</h4>
        <pre><code>curl -X POST {{ base_url }}/analyze \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: your-secret-api-key" \\
  -d '{
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "OKX",
    "interval": "1 Day",
    "risk": "Medium"
  }'</code></pre>

        <h4>Success Response (200 OK)</h4>
        <pre><code>{
  "status": "success",
  "analysis": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "exchange": "OKX",
    "interval": "1 Day",
    "prediction_date": "2024-09-25 19:00:00",
    "current_price": 65233.0,
    "currency": "USDT",
    "price_change_percent": 1.41,
    "predicted_price": 66150.25,
    "recommended_action": "Buy",
    "confidence_scores": {
      "action_confidence": 78.5,
      "price_confidence": 82.1
    },
    "risk_levels": {
      "risk_setting": "Medium",
      "buy_price": 62703.56,
      "sell_price": 66270.17
    },
    "trading_signal": {
      "action": "Buy",
      "confidence": 78.5,
      "target_price": 66150.25,
      "stop_loss": 62703.56,
      "take_profit": 66270.17
    }
  }
}</code></pre>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /assets</h3>
            <p>Retrieve all available assets organized by category with optional filtering.</p>
        </div>

        <h4>Query Parameters</h4>
        <table>
            <tr><th>Parameter</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td><code>type</code></td><td>string</td><td>No</td><td>Filter by asset type: "Stocks", "Cryptocurrency", "Forex", "Futures & Commodities", "Index Funds"</td></tr>
            <tr><td><code>market</code></td><td>string</td><td>No</td><td>Filter by market name (partial match)</td></tr>
            <tr><td><code>limit</code></td><td>integer</td><td>No</td><td>Limit number of results per category</td></tr>
        </table>

        <h4>Example Request</h4>
        <pre><code>curl -X GET "{{ base_url }}/assets?type=Stocks&market=S%26P%20500&limit=10" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h4>Success Response (200 OK)</h4>
        <pre><code>{
  "status": "success",
  "total_assets": 503,
  "asset_categories": {
    "Stocks": {
      "US S&P 500": {
        "count": 10,
        "assets": [
          {
            "symbol": "3M",
            "ticker": "MMM",
            "currency": "USD",
            "currency_name": "US Dollar"
          },
          {
            "symbol": "Apple Inc.",
            "ticker": "AAPL",
            "currency": "USD",
            "currency_name": "US Dollar"
          }
        ]
      }
    }
  },
  "filters_applied": {
    "asset_type": "Stocks",
    "market": "S&P 500",
    "limit": 10
  },
  "available_asset_types": [
    "Stocks",
    "Cryptocurrency",
    "Forex",
    "Futures & Commodities",
    "Index Funds"
  ]
}</code></pre>

        <div class="endpoint">
            <h3><span class="method post">POST</span> <span class="method get">GET</span> /indicators</h3>
            <p>Get candlestick data with comprehensive technical indicators. Returns OHLCV data along with all major technical indicators.</p>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> <span class="method get">GET</span> /indicators/compact</h3>
            <p><strong>🎯 Optimized for n8n & Automation</strong> - Returns latest indicator values only. Smaller response size, perfect for workflows.</p>
        </div>

        <h4>Request Parameters</h4>
        <table>
            <tr><th>Parameter</th><th>Type</th><th>Required</th><th>Default</th><th>Description</th></tr>
            <tr><td><code>asset</code></td><td>string</td><td>No</td><td>"Cryptocurrency"</td><td>Asset type: "Stocks", "Cryptocurrency", "Forex", "Index Fund", "Futures & Commodities"</td></tr>
            <tr><td><code>equity</code></td><td>string</td><td>✅ Yes</td><td>-</td><td>Specific instrument (e.g., "BTC", "AAPL")</td></tr>
            <tr><td><code>market</code></td><td>string</td><td>No</td><td>"USDT"</td><td>Market type (e.g., "USDT", "S&P 500")</td></tr>
            <tr><td><code>exchange</code></td><td>string</td><td>No</td><td>"Binance"</td><td>Exchange: "Binance", "OKX" (for crypto)</td></tr>
            <tr><td><code>bar</code></td><td>string</td><td>No</td><td>"1m"</td><td>Time interval: 1m, 3m, 5m, 15m, 30m, 1H, 2H, 4H, 1D</td></tr>
            <tr><td><code>limit</code></td><td>integer</td><td>No</td><td>100</td><td>Number of candlesticks (max 100)</td></tr>
            <tr><td><code>indicatorName</code></td><td>string</td><td>No</td><td>"all"</td><td>Specific indicator names (comma-separated) or "all"</td></tr>
            <tr><td><code>after</code></td><td>string</td><td>No</td><td>-</td><td>Pagination: records after timestamp</td></tr>
            <tr><td><code>before</code></td><td>string</td><td>No</td><td>-</td><td>Pagination: records before timestamp</td></tr>
        </table>

        <h4>Available Indicators</h4>
        <table>
            <tr><th>Category</th><th>Count</th><th>Examples</th></tr>
            <tr><td><strong>Volume</strong></td><td>9</td><td>MFI, ADI, OBV, CMF, VWAP</td></tr>
            <tr><td><strong>Volatility</strong></td><td>5</td><td>ATR, Bollinger Bands, Keltner Channel</td></tr>
            <tr><td><strong>Trend</strong></td><td>15</td><td>SMA, EMA, MACD, ADX, Ichimoku</td></tr>
            <tr><td><strong>Momentum</strong></td><td>11</td><td>RSI, Stochastic, Williams %R, KAMA</td></tr>
            <tr><td><strong>Others</strong></td><td>3</td><td>Daily Return, Cumulative Return</td></tr>
        </table>

        <h4>Example Request</h4>
        <pre><code>curl -X POST {{ base_url }}/indicators \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: your-secret-api-key" \\
  -d '{
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "bar": "1H",
    "limit": 50,
    "indicatorName": "all"
  }'</code></pre>

        <h4>Success Response (200 OK)</h4>
        <pre><code>{
  "status": "success",
  "data": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "interval": "1H",
    "candlesticks": [
      {
        "timestamp": "2024-01-01T00:00:00",
        "open": 42000.0,
        "high": 42500.0,
        "low": 41800.0,
        "close": 42300.0,
        "volume": 1234.56
      }
    ],
    "indicators": {
      "volume": {
        "money_flow_index": [45.2, 48.1, ...],
        "on_balance_volume": [1000, 1200, ...]
      },
      "volatility": {
        "average_true_range": [120.5, 115.2, ...],
        "bollinger_hband": [43000, 43200, ...]
      },
      "trend": {
        "sma_indicator": [42100, 42150, ...],
        "macd": [12.5, 15.2, ...]
      },
      "momentum": {
        "rsi": [65.2, 68.1, ...],
        "stoch": [45.2, 48.7, ...]
      },
      "others": {
        "daily_return": [0.012, -0.005, ...]
      }
    }
  },
  "metadata": {
    "total_records": 50,
    "limit": 50,
    "requested_indicators": "all",
    "has_more": false
  }
}</code></pre>

        <h4>Compact Endpoint Parameters (Same as above, with optimized defaults)</h4>
        <table>
            <tr><th>Parameter</th><th>Default (Compact)</th><th>Description</th></tr>
            <tr><td><code>limit</code></td><td>20</td><td>Smaller default for faster responses</td></tr>
            <tr><td><code>indicatorName</code></td><td>Popular indicators</td><td>Returns RSI, MACD, SMA, EMA, Bollinger Bands, ATR, OBV by default</td></tr>
        </table>

        <h4>Compact Response Format</h4>
        <pre><code>{
  "status": "success",
  "data": {
    "latest_candle": {
      "close": 118696.37,
      "high": 119573.53,
      "timestamp": "2025-07-22T03:00:00"
    },
    "indicators": {
      "rsi": {
        "category": "momentum",
        "latest_value": 68.98,
        "values_count": 30
      }
    },
    "latest_indicator_values": {
      "rsi": 68.98,
      "macd": 2808.81
    }
  },
  "metadata": {
    "response_type": "compact",
    "indicators_count": 10
  }
}</code></pre>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /health</h3>
            <p>Health check endpoint to verify API availability.</p>
        </div>

        <h4>Response (200 OK)</h4>
        <pre><code>{
  "status": "healthy",
  "service": "trading-analysis-api"
}</code></pre>

        <h2 id="examples">💡 Usage Examples</h2>

        <h3>List All Assets</h3>
        <pre><code>curl -X GET "{{ base_url }}/assets" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h3>List Cryptocurrency Assets</h3>
        <pre><code>curl -X GET "{{ base_url }}/assets?type=Cryptocurrency" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h3>List S&P 500 Stocks (Limited)</h3>
        <pre><code>curl -X GET "{{ base_url }}/assets?type=Stocks&market=S%26P%20500&limit=50" \
  -H "X-API-Key: your-secret-api-key"</code></pre>

        <h3>Get All Technical Indicators</h3>
        <pre><code>curl -X POST "{{ base_url }}/indicators" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key" \
  -d '{
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "bar": "1H",
    "limit": 100,
    "indicatorName": "all"
  }'</code></pre>

        <h3>Get Specific Indicators</h3>
        <pre><code>curl -X POST "{{ base_url }}/indicators" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key" \
  -d '{
    "asset": "Cryptocurrency",
    "equity": "ETH",
    "market": "USDT",
    "bar": "5m",
    "limit": 50,
    "indicatorName": "rsi,macd,bollinger_hband,sma_indicator"
  }'</code></pre>

        <h3>Get Stock Indicators</h3>
        <pre><code>curl -X POST "{{ base_url }}/indicators" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secret-api-key" \
  -d '{
    "asset": "Stocks",
    "equity": "AAPL",
    "market": "S&P 500",
    "bar": "1D",
    "limit": 100,
    "indicatorName": "all"
  }'</code></pre>

        <h3>Stock Analysis</h3>
        <pre><code>{
  "asset": "Stocks",
  "equity": "AAPL",
  "market": "S&P 500",
  "interval": "1 Hour",
  "risk": "Medium"
}</code></pre>

        <h3>Cryptocurrency Analysis</h3>

        <h4>Binance Spot Trading</h4>
        <pre><code>{
  "asset": "Cryptocurrency",
  "equity": "BTC",
  "market": "USDT",
  "exchange": "Binance",
  "interval": "1 Day",
  "risk": "High"
}</code></pre>

        <h4>OKX Spot Trading</h4>
        <pre><code>{
  "asset": "Cryptocurrency",
  "equity": "BTC",
  "market": "USDT",
  "exchange": "OKX",
  "interval": "1 Day",
  "risk": "Medium"
}</code></pre>

        <h4>OKX Perpetual Swaps</h4>
        <pre><code>{
  "asset": "Cryptocurrency",
  "equity": "BTC",
  "market": "SWAP-USD",
  "exchange": "OKX",
  "interval": "1 Hour",
  "risk": "High"
}</code></pre>

        <h4>OKX Futures</h4>
        <pre><code>{
  "asset": "Cryptocurrency",
  "equity": "BTC",
  "market": "FUTURES-USD",
  "exchange": "OKX",
  "interval": "1 Day",
  "risk": "High"
}</code></pre>

        <h3>Forex Analysis</h3>
        <pre><code>{
  "asset": "Forex",
  "equity": "EUR/USD",
  "interval": "1 Hour",
  "risk": "Low"
}</code></pre>

        <h2 id="okx-features">🚀 OKX Advanced Features</h2>

        <p>Our API now supports <strong>OKX exchange</strong> with comprehensive coverage of spot, derivatives, and futures markets:</p>

        <h3>📊 Instrument Types</h3>
        <ul>
            <li><strong>SPOT</strong> (657 instruments): Traditional spot trading pairs like BTC-USDT, ETH-USD</li>
            <li><strong>SWAP</strong> (246 instruments): Perpetual contracts like BTC-USD-SWAP, ETH-USD-SWAP</li>
            <li><strong>FUTURES</strong> (23 instruments): Dated futures contracts like BTC-USD-250725</li>
        </ul>

        <h3>⚡ Key Benefits</h3>
        <ul>
            <li><strong>Real-time data</strong> from OKX API with 300 candlesticks per request</li>
            <li><strong>Multiple timeframes</strong>: 1m, 5m, 15m, 30m, 1H, 2H, 4H, 6H, 12H, 1D, 1W, 1M</li>
            <li><strong>Advanced derivatives</strong>: Perpetual swaps and futures analysis</li>
            <li><strong>High leverage trading</strong>: Perfect for derivatives strategies</li>
            <li><strong>Institutional grade</strong>: Same data used by professional traders</li>
        </ul>

        <h3>🎯 Market Selection Guide</h3>
        <table>
            <tr><th>Market</th><th>Use Case</th><th>Risk Level</th><th>Example</th></tr>
            <tr><td><code>USDT</code></td><td>Spot trading</td><td>Low-Medium</td><td>BTC spot price analysis</td></tr>
            <tr><td><code>SWAP-USD</code></td><td>Perpetual contracts</td><td>Medium-High</td><td>Leveraged BTC positions</td></tr>
            <tr><td><code>FUTURES-USD</code></td><td>Dated contracts</td><td>High</td><td>Quarterly BTC futures</td></tr>
        </table>

        <h2 id="n8n">🔄 n8n Integration</h2>

        <h3>HTTP Request Node Configuration</h3>

        <h4>OKX Spot Trading</h4>
        <pre><code>{
  "method": "POST",
  "url": "{{ base_url }}/analyze",
  "headers": {
    "X-API-Key": "{% raw %}{{$env.TRADING_API_KEY}}{% endraw %}"
  },
  "body": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "OKX",
    "interval": "1 Hour",
    "risk": "Medium"
  }
}</code></pre>

        <h4>OKX Perpetual Swaps</h4>
        <pre><code>{
  "method": "POST",
  "url": "{{ base_url }}/analyze",
  "headers": {
    "X-API-Key": "{% raw %}{{$env.TRADING_API_KEY}}{% endraw %}"
  },
  "body": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "SWAP-USD",
    "exchange": "OKX",
    "interval": "15 Minute",
    "risk": "High"
  }
}</code></pre>

        <h3>Conditional Logic (IF Node)</h3>
        <pre><code>// Check if action is Buy and confidence > 75%
return items[0].json.analysis.recommended_action === 'Buy' && 
       items[0].json.analysis.confidence_scores.action_confidence > 75;</code></pre>

        <h3>Trading Alert Message</h3>
        <pre><code>// Format trading alert message
const analysis = items[0].json.analysis;
return {% raw %}`🚨 Trading Alert: ${analysis.recommended_action} ${analysis.equity}
Current: $${analysis.current_price}
Target: $${analysis.predicted_price}
Confidence: ${analysis.confidence_scores.action_confidence}%`{% endraw %};</code></pre>

        <h2 id="errors">⚠️ Error Handling</h2>

        <div class="error">
            <h4>401 Unauthorized</h4>
            <pre><code>{ "error": "Invalid or missing API key" }</code></pre>
        </div>

        <div class="error">
            <h4>400 Bad Request</h4>
            <pre><code>{ "error": "equity parameter is required" }</code></pre>
        </div>

        <div class="error">
            <h4>500 Internal Server Error</h4>
            <pre><code>{ "error": "Analysis failed: [specific error message]" }</code></pre>
        </div>

        <h2>📊 Supported Assets & Intervals</h2>

        <h3>Asset Types</h3>
        <ul>
            <li><strong>Stocks:</strong> S&P 500, NASDAQ, Dow Jones, and international indices</li>
            <li><strong>Cryptocurrency:</strong> Bitcoin, Ethereum, and major altcoins</li>
            <li><strong>Forex:</strong> Major currency pairs (EUR/USD, GBP/USD, etc.)</li>
            <li><strong>Futures & Commodities:</strong> Gold, Oil, Agricultural products</li>
            <li><strong>Index Funds:</strong> Major market indices</li>
        </ul>

        <h3>Time Intervals</h3>
        <div class="warning">
            <strong>Stocks/Forex/Futures/Index Funds:</strong> 5 Minute, 15 Minute, 30 Minute, 1 Hour, 1 Day, 1 Week<br>
            <strong>Cryptocurrency:</strong> 1 Minute, 3 Minute, 5 Minute, 15 Minute, 30 Minute, 1 Hour, 6 Hour, 12 Hour, 1 Day, 1 Week
        </div>

        <h2>🛡️ Best Practices</h2>
        <ul>
            <li><strong>Rate Limiting:</strong> Recommended maximum 60 requests per minute</li>
            <li><strong>Confidence Thresholds:</strong> Set minimum confidence levels (e.g., 75%) for automated actions</li>
            <li><strong>Risk Management:</strong> Always implement stop-loss and take-profit levels</li>
            <li><strong>Error Handling:</strong> Implement proper try-catch blocks in your workflows</li>
            <li><strong>Monitoring:</strong> Log all API responses for audit and debugging</li>
        </ul>

        <div class="warning">
            <strong>⚠️ Disclaimer:</strong> This API provides educational and informational trading signals. Always conduct your own research and consider consulting with financial advisors before making investment decisions. Past performance does not guarantee future results.
        </div>

        <footer>
            <p>🚀 Trading Analysis API | Built with Flask & Machine Learning | Version 1.0</p>
        </footer>
    </div>
</body>
</html>
"""

def authenticate_api_key():
    """Validate API key from X-API-Key header"""
    api_key = os.getenv('API_KEY')
    if not api_key:
        return False
    
    provided_key = request.headers.get('X-API-Key')
    return provided_key == api_key

@app.route('/')
@app.route('/docs')
def documentation():
    """Serve API documentation page"""
    base_url = request.url_root.rstrip('/')
    return render_template_string(DOCS_TEMPLATE, base_url=base_url)

@app.route('/analyze', methods=['POST'])
def analyze():
    # Authenticate API key
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401
    
    gc.collect()
    
    try:
        # Get parameters from request JSON
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        # Extract parameters with defaults
        asset = data.get('asset', 'Stocks')
        risk = data.get('risk', 'Medium')
        interval = data.get('interval', '1 Day')
        equity = data.get('equity')
        market = data.get('market')
        exchange_param = data.get('exchange')  # Optional exchange parameter
        
        if not equity:
            return jsonify({"error": "equity parameter is required"}), 400
        
        indication = 'Predicted'
        
        # Process asset type and set exchange
        if asset in ['Index Fund', 'Forex', 'Futures & Commodities', 'Stocks']:
            exchange = 'Yahoo! Finance'
            app_data.exchange_data(exchange)

            if asset == 'Stocks':
                if not market:
                    market = 'S&P 500'  # Default market

                # Map common market names to actual data names
                market_mapping = {
                    'S&P 500': 'US S&P 500',
                    'NASDAQ': 'US NASDAQ 100',
                    'Dow Jones': 'US Dow Jones',
                    'Russell 1000': 'US Russell 1000',
                    'FTSE 100': 'British FTSE 100',
                    'DAX': 'German DAX',
                    'CAC 40': 'French CAC 40',
                    'Nifty 50': 'Indian Nifty 50',
                    'ASX 200': 'Australian S&P ASX 200'
                }

                # Use mapped market name if available
                actual_market = market_mapping.get(market, market)

                # Check if equity exists in the specified market
                stock_matches = app_data.df_stocks[
                    (app_data.df_stocks['Company'] == equity) &
                    (app_data.df_stocks['Index Fund'] == actual_market)
                ]

                if stock_matches.empty:
                    # Try to find the stock in any market
                    all_stock_matches = app_data.df_stocks[app_data.df_stocks['Company'] == equity]
                    if all_stock_matches.empty:
                        return jsonify({"error": f"Stock '{equity}' not found in any market"}), 400
                    else:
                        # Use the first market where this stock is found
                        actual_market = all_stock_matches.iloc[0]['Index Fund']

                app_data.market_data(actual_market)
                assets = app_data.stocks if hasattr(app_data, 'stocks') else []
                asset_label = f'{actual_market} Companies'
                market = actual_market  # Update market to the actual name for later use

            elif asset == 'Index Fund':
                # Check if equity exists in indexes
                index_matches = app_data.df_indexes[app_data.df_indexes['Indexes'] == equity]
                if index_matches.empty:
                    return jsonify({"error": f"Index '{equity}' not found"}), 400
                assets = app_data.indexes
                asset_label = asset

            elif asset == 'Futures & Commodities':
                # Check if equity exists in futures
                futures_matches = app_data.df_futures[app_data.df_futures['Futures'] == equity]
                if futures_matches.empty:
                    return jsonify({"error": f"Future/Commodity '{equity}' not found"}), 400
                assets = app_data.futures
                asset_label = asset

            elif asset == 'Forex':
                # Check if equity exists in forex
                forex_matches = app_data.df_forex[app_data.df_forex['Currencies'] == equity]
                if forex_matches.empty:
                    return jsonify({"error": f"Forex pair '{equity}' not found"}), 400
                assets = app_data.forex
                asset_label = asset

            # Set currency and market based on asset type
            if asset == 'Futures & Commodities':
                currency = 'USD'
                market = None
            elif asset == 'Index Fund':
                currency = 'Pts'
                market = None
            elif asset == 'Forex':
                forex_data = app_data.df_forex[app_data.df_forex['Currencies'] == equity]
                if not forex_data.empty:
                    currency = forex_data['Currency'].iloc[0]
                    market = forex_data['Market'].iloc[0]
                else:
                    currency = 'USD'
                    market = None
            elif asset == 'Stocks':
                stock_data = app_data.df_stocks[
                    (app_data.df_stocks['Company'] == equity) &
                    (app_data.df_stocks['Index Fund'] == market)
                ]
                if not stock_data.empty:
                    currency = stock_data['Currency'].iloc[0]
                else:
                    currency = 'USD'
                asset = 'Stock'

            volitility_index = 0
            
        elif asset in ['Cryptocurrency']:
            # Use exchange parameter if provided, otherwise default to Binance
            exchange = exchange_param if exchange_param in ['Binance', 'OKX'] else 'Binance'
            app_data.exchange_data(exchange)
            markets = app_data.markets

            if not market:
                market = 'USDT'  # Default market

            # Select the appropriate dataframe based on exchange
            crypto_df = app_data.df_okx if exchange == 'OKX' else app_data.df_crypto

            # Check if the cryptocurrency exists in the specified market
            crypto_matches = crypto_df[
                (crypto_df['Currency'] == equity) &
                (crypto_df['Market'] == market)
            ]

            if crypto_matches.empty:
                # Try to find the crypto in any market
                all_crypto_matches = crypto_df[crypto_df['Currency'] == equity]
                if all_crypto_matches.empty:
                    return jsonify({"error": f"Cryptocurrency '{equity}' not found on {exchange}"}), 400
                else:
                    # Use the first market where this crypto is found
                    market = all_crypto_matches.iloc[0]['Market']

            app_data.market_data(market)
            assets = app_data.assets if hasattr(app_data, 'assets') else []
            currency = app_data.currency if hasattr(app_data, 'currency') else market
            asset_label = asset
            volitility_index = 2
        
        # Validate that the equity exists in the assets list
        if assets is not None and len(assets) > 0:
            # Convert to list if it's a numpy array to avoid ambiguous truth value error
            assets_list = list(assets) if hasattr(assets, '__iter__') else []
            if equity not in assets_list:
                return jsonify({"error": f"'{equity}' not found in {asset_label}. Please check the spelling or use the /assets endpoint to see available options."}), 400

        # Perform analysis with improved error handling
        try:
            analysis = Visualization(exchange, interval, equity, indication, action_model, price_model, market)
            analysis_day = Indications(exchange, '1 Day', equity, market)

            # Check if we got valid data
            if analysis.df.empty:
                return jsonify({
                    "error": "No market data available",
                    "message": f"No market data available for '{equity}'. This could be due to:",
                    "possible_causes": [
                        "Market closure (weekends/holidays)",
                        "Delisted asset or symbol changes",
                        "Data provider issues",
                        "Invalid ticker symbol"
                    ],
                    "suggestion": "Please verify the asset name and try again during market hours.",
                    "asset_info": {
                        "equity": equity,
                        "market": market,
                        "exchange": exchange,
                        "interval": interval,
                        "asset_type": asset_label
                    }
                }), 400

        except Exception as e:
            # Check if this is a data availability issue (common with yfinance)
            error_message = str(e).lower()
            if any(keyword in error_message for keyword in ['no data', 'empty', 'not found', 'invalid', 'ticker']):
                return jsonify({
                    "error": "Market data temporarily unavailable",
                    "message": f"Unable to retrieve real-time market data for '{equity}' from {exchange}. This could be due to:",
                    "possible_causes": [
                        "Yahoo Finance API temporary outage",
                        "Network connectivity issues",
                        "Market closure (weekends/holidays)",
                        "Symbol delisting or changes",
                        "Rate limiting from data provider"
                    ],
                    "suggestion": "Please try again later or contact support if the issue persists.",
                    "asset_info": {
                        "equity": equity,
                        "market": market,
                        "exchange": exchange,
                        "interval": interval,
                        "asset_type": asset_label
                    },
                    "debug_error": str(e)
                }), 503  # Service Unavailable
            else:
                return jsonify({"error": f"Analysis failed: {str(e)}"}), 500
        
        # Extract results with error handling
        try:
            requested_date = str(analysis.df.index[-1])
            current_price = float(analysis.df['Adj Close'][-1])
            change = float(analysis.df['Adj Close'].pct_change()[-1]) * 100
            requested_prediction_price = float(analysis.requested_prediction_price)
            requested_prediction_action = analysis.requested_prediction_action

            # Calculate risk levels
            if analysis_day.df.empty:
                return jsonify({"error": f"Unable to calculate risk levels for '{equity}'. Insufficient daily data."}), 400

            risks = {
                'Low': [analysis_day.df['S1'].values[-1], analysis_day.df['R1'].values[-1]],
                'Medium': [analysis_day.df['S2'].values[-1], analysis_day.df['R2'].values[-1]],
                'High': [analysis_day.df['S3'].values[-1], analysis_day.df['R3'].values[-1]]
            }
            buy_price = float(risks[risk][0])
            sell_price = float(risks[risk][1])

        except (IndexError, KeyError, ValueError) as e:
            return jsonify({"error": f"Data processing error for '{equity}': {str(e)}. This may be due to insufficient market data or market closure."}), 400
        
        # Format response
        response_data = {
            "status": "success",
            "analysis": {
                "asset": asset_label,
                "equity": equity,
                "exchange": exchange,
                "interval": interval,
                "prediction_date": requested_date,
                "current_price": current_price,
                "currency": currency,
                "price_change_percent": round(change, 2),
                "predicted_price": requested_prediction_price,
                "recommended_action": requested_prediction_action,
                "confidence_scores": {
                    "action_confidence": analysis.score_action,
                    "price_confidence": analysis.score_price
                },
                "risk_levels": {
                    "risk_setting": risk,
                    "buy_price": buy_price,
                    "sell_price": sell_price
                },
                "trading_signal": {
                    "action": requested_prediction_action,
                    "confidence": analysis.score_action,
                    "target_price": requested_prediction_price,
                    "stop_loss": buy_price if requested_prediction_action == "Buy" else sell_price,
                    "take_profit": sell_price if requested_prediction_action == "Buy" else buy_price
                }
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@app.route('/assets', methods=['GET'])
def list_assets():
    """List all available assets by category"""
    # Authenticate API key
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401

    try:
        # Get query parameters for filtering
        asset_type = request.args.get('type', None)  # Optional filter by asset type
        market = request.args.get('market', None)    # Optional filter by market
        limit = request.args.get('limit', None)      # Optional limit results

        # Convert limit to integer if provided
        if limit:
            try:
                limit = int(limit)
                if limit <= 0:
                    return jsonify({"error": "Limit must be a positive integer"}), 400
            except ValueError:
                return jsonify({"error": "Limit must be a valid integer"}), 400

        assets_data = {
            "status": "success",
            "total_assets": 0,
            "asset_categories": {}
        }

        # Load and process Stocks
        if not asset_type or asset_type.lower() in ['stocks', 'stock']:
            try:
                df_stocks = app_data.df_stocks
                stocks_by_market = {}

                for index_fund in df_stocks['Index Fund'].unique():
                    if pd.notna(index_fund):
                        market_stocks = df_stocks[df_stocks['Index Fund'] == index_fund]
                        if not market or market.lower() in index_fund.lower():
                            stock_list = []
                            for _, row in market_stocks.iterrows():
                                if pd.notna(row['Company']) and pd.notna(row['Ticker']):
                                    stock_list.append({
                                        "symbol": row['Company'],
                                        "ticker": row['Ticker'],
                                        "currency": row['Currency'] if pd.notna(row['Currency']) else 'USD',
                                        "currency_name": row['Currency_Name'] if pd.notna(row['Currency_Name']) else 'US Dollar'
                                    })

                            if stock_list:
                                if limit:
                                    stock_list = stock_list[:limit]
                                stocks_by_market[index_fund] = {
                                    "count": len(stock_list),
                                    "assets": stock_list
                                }

                if stocks_by_market:
                    assets_data["asset_categories"]["Stocks"] = stocks_by_market
                    assets_data["total_assets"] += sum(market["count"] for market in stocks_by_market.values())
            except Exception as e:
                print(f"Error loading stocks: {e}")

        # Load and process Cryptocurrency
        if not asset_type or asset_type.lower() in ['cryptocurrency', 'crypto']:
            try:
                df_crypto = app_data.df_crypto
                crypto_by_market = {}

                for market_type in df_crypto['Market'].unique():
                    if pd.notna(market_type):
                        market_crypto = df_crypto[df_crypto['Market'] == market_type]
                        if not market or market.lower() in market_type.lower():
                            crypto_list = []
                            for _, row in market_crypto.iterrows():
                                if pd.notna(row['Currency']) and pd.notna(row['Binance Pair']):
                                    crypto_list.append({
                                        "symbol": row['Currency'],
                                        "pair": row['Binance Pair'],
                                        "market": row['Market']
                                    })

                            if crypto_list:
                                if limit:
                                    crypto_list = crypto_list[:limit]
                                crypto_by_market[market_type] = {
                                    "count": len(crypto_list),
                                    "assets": crypto_list
                                }

                if crypto_by_market:
                    assets_data["asset_categories"]["Cryptocurrency"] = crypto_by_market
                    assets_data["total_assets"] += sum(market["count"] for market in crypto_by_market.values())
            except Exception as e:
                print(f"Error loading cryptocurrency: {e}")

        # Load and process Forex
        if not asset_type or asset_type.lower() == 'forex':
            try:
                df_forex = app_data.df_forex
                forex_list = []

                for _, row in df_forex.iterrows():
                    if pd.notna(row['Currencies']) and pd.notna(row['Ticker']):
                        if not market or market.lower() in row['Currencies'].lower():
                            forex_list.append({
                                "symbol": row['Currencies'],
                                "ticker": row['Ticker'],
                                "base_currency": row['Currency'] if pd.notna(row['Currency']) else '',
                                "quote_currency": row['Market'] if pd.notna(row['Market']) else ''
                            })

                if forex_list:
                    if limit:
                        forex_list = forex_list[:limit]
                    assets_data["asset_categories"]["Forex"] = {
                        "count": len(forex_list),
                        "assets": forex_list
                    }
                    assets_data["total_assets"] += len(forex_list)
            except Exception as e:
                print(f"Error loading forex: {e}")

        # Load and process Futures & Commodities
        if not asset_type or asset_type.lower() in ['futures', 'commodities', 'futures & commodities']:
            try:
                df_futures = app_data.df_futures
                futures_list = []

                for _, row in df_futures.iterrows():
                    if pd.notna(row['Futures']) and pd.notna(row['Ticker']):
                        if not market or market.lower() in row['Futures'].lower():
                            futures_list.append({
                                "symbol": row['Futures'],
                                "ticker": row['Ticker']
                            })

                if futures_list:
                    if limit:
                        futures_list = futures_list[:limit]
                    assets_data["asset_categories"]["Futures & Commodities"] = {
                        "count": len(futures_list),
                        "assets": futures_list
                    }
                    assets_data["total_assets"] += len(futures_list)
            except Exception as e:
                print(f"Error loading futures: {e}")

        # Load and process Index Funds
        if not asset_type or asset_type.lower() in ['index', 'indexes', 'index fund', 'index funds']:
            try:
                df_indexes = app_data.df_indexes
                indexes_list = []

                for _, row in df_indexes.iterrows():
                    if pd.notna(row['Indexes']) and pd.notna(row['Ticker']):
                        if not market or market.lower() in row['Indexes'].lower():
                            indexes_list.append({
                                "symbol": row['Indexes'],
                                "ticker": row['Ticker']
                            })

                if indexes_list:
                    if limit:
                        indexes_list = indexes_list[:limit]
                    assets_data["asset_categories"]["Index Funds"] = {
                        "count": len(indexes_list),
                        "assets": indexes_list
                    }
                    assets_data["total_assets"] += len(indexes_list)
            except Exception as e:
                print(f"Error loading indexes: {e}")

        # Add metadata
        assets_data["filters_applied"] = {
            "asset_type": asset_type,
            "market": market,
            "limit": limit
        }

        assets_data["available_asset_types"] = [
            "Stocks",
            "Cryptocurrency",
            "Forex",
            "Futures & Commodities",
            "Index Funds"
        ]

        return jsonify(assets_data)

    except Exception as e:
        return jsonify({"error": f"Failed to retrieve assets: {str(e)}"}), 500

@app.route('/debug', methods=['POST'])
def debug_analysis():
    """Debug endpoint to test data sourcing"""
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401

    try:
        data = request.get_json() if request.get_json() else {}
        equity = data.get('equity', 'Abbott')

        # Test basic data sourcing
        test_data = Data_Sourcing()
        test_data.exchange_data('Yahoo! Finance')
        test_data.market_data('S&P 500')

        # Test if we can create basic indicators
        from app.technical_indicators import Technical_Calculations
        tech_calc = Technical_Calculations('Yahoo! Finance', '1 Day', equity, 'US S&P 500')

        return jsonify({
            "status": "debug_success",
            "equity": equity,
            "df_shape": tech_calc.df.shape,
            "df_columns": list(tech_calc.df.columns),
            "last_date": str(tech_calc.df.index[-1]) if not tech_calc.df.empty else "No data",
            "data_available": not tech_calc.df.empty,
            "ticker_used": getattr(tech_calc, 'ticker', 'Unknown')
        })

    except Exception as e:
        import traceback
        return jsonify({
            "error": str(e),
            "traceback": traceback.format_exc(),
            "status": "debug_failed"
        }), 500

@app.route('/test', methods=['GET'])
def test_data_sources():
    """Test endpoint to verify data sources are working"""
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401

    test_results = {}

    # Test different data sources
    test_cases = [
        {"asset": "Stocks", "equity": "Apple", "market": "US Dow Jones"},
        {"asset": "Index Fund", "equity": "S&P 500", "market": None},
        {"asset": "Cryptocurrency", "equity": "BTC", "market": "USDT", "exchange": "Binance"},
        {"asset": "Cryptocurrency", "equity": "BTC", "market": "USDT", "exchange": "OKX"},
        {"asset": "Cryptocurrency", "equity": "BTC", "market": "SWAP-USD", "exchange": "OKX"},
        {"asset": "Cryptocurrency", "equity": "BTC", "market": "FUTURES-USD", "exchange": "OKX"}
    ]

    for i, test_case in enumerate(test_cases):
        try:
            test_data = Data_Sourcing()

            if test_case["asset"] == "Stocks":
                test_data.exchange_data('Yahoo! Finance')
                test_data.market_data(test_case["market"])
                test_data.intervals('1 Day')
                test_data.apis(test_case["equity"])
            elif test_case["asset"] == "Index Fund":
                test_data.exchange_data('Yahoo! Finance')
                test_data.intervals('1 Day')
                test_data.apis(test_case["equity"])
            elif test_case["asset"] == "Cryptocurrency":
                exchange = test_case.get("exchange", "Binance")
                test_data.exchange_data(exchange)
                test_data.market_data(test_case["market"])
                test_data.intervals('1 Day')
                test_data.apis(test_case["equity"])

            test_results[f"test_{i+1}"] = {
                "asset_type": test_case["asset"],
                "equity": test_case["equity"],
                "market": test_case["market"],
                "exchange": test_case.get("exchange", "N/A"),
                "status": "success" if not test_data.df.empty else "no_data",
                "data_shape": test_data.df.shape,
                "ticker": getattr(test_data, 'ticker', getattr(test_data, 'ticker_market', 'Unknown'))
            }

        except Exception as e:
            test_results[f"test_{i+1}"] = {
                "asset_type": test_case["asset"],
                "equity": test_case["equity"],
                "market": test_case["market"],
                "exchange": test_case.get("exchange", "N/A"),
                "status": "error",
                "error": str(e)
            }

    return jsonify({
        "status": "test_complete",
        "results": test_results,
        "summary": {
            "total_tests": len(test_cases),
            "successful": len([r for r in test_results.values() if r["status"] == "success"]),
            "no_data": len([r for r in test_results.values() if r["status"] == "no_data"]),
            "errors": len([r for r in test_results.values() if r["status"] == "error"])
        }
    })

@app.route('/indicators', methods=['GET', 'POST'])
def get_indicators():
    """Get candlestick data with comprehensive technical indicators"""
    # Authenticate API key
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401

    gc.collect()

    try:
        # Get parameters from request (support both GET and POST)
        if request.method == 'POST':
            data = request.get_json() or {}
        else:
            data = request.args.to_dict()

        # Extract required parameters
        asset = data.get('asset', 'Cryptocurrency')
        equity = data.get('equity')
        market = data.get('market', 'USDT')
        exchange = data.get('exchange', 'Binance')

        # Extract optional parameters
        after = data.get('after')  # Pagination - not fully implemented in underlying APIs
        before = data.get('before')  # Pagination - not fully implemented in underlying APIs
        bar = data.get('bar', '1m')  # Time interval
        limit = int(data.get('limit', 100))  # Number of results
        indicator_name = data.get('indicatorName', 'all')  # Specific indicator or 'all'

        if not equity:
            return jsonify({"error": "equity parameter is required"}), 400

        # Validate limit
        if limit > 100:
            limit = 100
        elif limit < 1:
            limit = 1

        # Map bar parameter to interval format expected by the system
        interval_mapping = {
            '1m': '1 Minute',
            '3m': '3 Minute',
            '5m': '5 Minute',
            '15m': '15 Minute',
            '30m': '30 Minute',
            '1H': '1 Hour',
            '2H': '2 Hour',
            '4H': '4 Hour',
            '6H': '6 Hour',
            '12H': '12 Hour',
            '1D': '1 Day',
            '1W': '1 Week',
            '1M': '1 Month'
        }

        interval = interval_mapping.get(bar, '1 Day')

        # Validate asset type and set appropriate exchange
        if asset in ['Index Fund', 'Forex', 'Futures & Commodities', 'Stocks']:
            exchange = 'Yahoo! Finance'

        elif asset == 'Cryptocurrency':
            if exchange not in ['Binance', 'OKX']:
                exchange = 'Binance'  # Default to Binance for crypto

        # Create comprehensive indicators instance
        try:
            indicators_calc = ComprehensiveIndicators(exchange, interval, equity, market, limit)
        except ValueError as e:
            return jsonify({
                "error": "Data not available",
                "message": str(e),
                "asset_info": {
                    "asset": asset,
                    "equity": equity,
                    "market": market,
                    "exchange": exchange,
                    "interval": bar
                }
            }), 400

        # Get candlestick data
        candlesticks = indicators_calc.get_candlestick_data()

        # Get indicators based on request
        if indicator_name == 'all':
            indicators = indicators_calc.calculate_all_indicators()
            # Convert pandas Series to lists for JSON serialization
            for category in indicators:
                for name, values in indicators[category].items():
                    if hasattr(values, 'tolist'):
                        indicators[category][name] = values.tolist()
                    elif hasattr(values, 'values'):
                        indicators[category][name] = values.values.tolist()
                    # Handle NaN values for better JSON compatibility
                    if isinstance(indicators[category][name], list):
                        indicators[category][name] = [
                            None if (isinstance(x, float) and (x != x or str(x).lower() == 'nan')) else x
                            for x in indicators[category][name]
                        ]
        else:
            # Handle specific indicator names (can be comma-separated)
            indicator_names = [name.strip() for name in indicator_name.split(',')]
            indicators = indicators_calc.get_indicators_by_name(indicator_names)
            # Clean up NaN values in specific indicators too
            for name, data in indicators.items():
                if isinstance(data, dict) and 'values' in data:
                    values = data['values']
                    if isinstance(values, list):
                        data['values'] = [
                            None if (isinstance(x, float) and (x != x or str(x).lower() == 'nan')) else x
                            for x in values
                        ]

        # Prepare response
        response_data = {
            "status": "success",
            "data": {
                "asset": asset,
                "equity": equity,
                "market": market,
                "exchange": exchange,
                "interval": bar,
                "candlesticks": candlesticks,
                "indicators": indicators
            },
            "metadata": {
                "total_records": len(candlesticks),
                "limit": limit,
                "requested_indicators": indicator_name,
                "has_more": False  # Pagination not fully implemented
            }
        }

        return jsonify(response_data)

    except Exception as e:
        return jsonify({
            "error": f"Failed to retrieve indicators: {str(e)}",
            "message": "Please check your parameters and try again"
        }), 500

@app.route('/indicators/compact', methods=['GET', 'POST'])
def get_indicators_compact():
    """Get candlestick data with technical indicators - optimized for n8n and smaller responses"""
    # Authenticate API key
    if not authenticate_api_key():
        return jsonify({"error": "Invalid or missing API key"}), 401

    gc.collect()

    try:
        # Get parameters from request (support both GET and POST)
        if request.method == 'POST':
            data = request.get_json() or {}
        else:
            data = request.args.to_dict()

        # Extract required parameters
        asset = data.get('asset', 'Cryptocurrency')
        equity = data.get('equity')
        market = data.get('market', 'USDT')
        exchange = data.get('exchange', 'Binance')

        # Extract optional parameters with smaller defaults for compact response
        bar = data.get('bar', '1H')  # Time interval
        limit = min(int(data.get('limit', 20)), 50)  # Smaller default limit for compact response
        indicator_name = data.get('indicatorName', 'rsi,macd,sma_indicator,bollinger_hband,bollinger_lband')  # Default to popular indicators

        if not equity:
            return jsonify({"error": "equity parameter is required"}), 400

        # Map bar parameter to interval format expected by the system
        interval_mapping = {
            '1m': '1 Minute', '3m': '3 Minute', '5m': '5 Minute',
            '15m': '15 Minute', '30m': '30 Minute', '1H': '1 Hour',
            '2H': '2 Hour', '4H': '4 Hour', '6H': '6 Hour',
            '12H': '12 Hour', '1D': '1 Day', '1W': '1 Week', '1M': '1 Month'
        }

        interval = interval_mapping.get(bar, '1 Hour')

        # Validate asset type and set appropriate exchange
        if asset in ['Index Fund', 'Forex', 'Futures & Commodities', 'Stocks']:
            exchange = 'Yahoo! Finance'
        elif asset == 'Cryptocurrency':
            if exchange not in ['Binance', 'OKX']:
                exchange = 'Binance'  # Default to Binance for crypto

        # Create comprehensive indicators instance
        try:
            indicators_calc = ComprehensiveIndicators(exchange, interval, equity, market, limit)
        except ValueError as e:
            return jsonify({
                "error": "Data not available",
                "message": str(e),
                "asset_info": {
                    "asset": asset,
                    "equity": equity,
                    "market": market,
                    "exchange": exchange,
                    "interval": bar
                }
            }), 400

        # Get candlestick data
        candlesticks = indicators_calc.get_candlestick_data()

        # Get indicators - for compact response, limit to specific indicators
        if indicator_name == 'all':
            # For compact response, return only the most popular indicators
            popular_indicators = [
                'rsi', 'macd', 'macd_signal', 'sma_indicator', 'ema_indicator',
                'bollinger_hband', 'bollinger_lband', 'bollinger_mavg',
                'average_true_range', 'on_balance_volume'
            ]
            indicators = indicators_calc.get_indicators_by_name(popular_indicators)
        else:
            # Handle specific indicator names (can be comma-separated)
            indicator_names = [name.strip() for name in indicator_name.split(',')]
            indicators = indicators_calc.get_indicators_by_name(indicator_names)

        # Prepare compact response with only latest values for each indicator
        compact_indicators = {}
        latest_values = {}

        for name, data in indicators.items():
            if isinstance(data, dict) and 'values' in data:
                values = data['values']
                if isinstance(values, list) and len(values) > 0:
                    # Get the latest non-null value
                    latest_value = None
                    for val in reversed(values):
                        if val is not None and str(val).lower() != 'nan':
                            latest_value = val
                            break

                    compact_indicators[name] = {
                        'category': data['category'],
                        'latest_value': latest_value,
                        'values_count': len(values)
                    }
                    latest_values[name] = latest_value

        # Get latest candlestick data
        latest_candle = candlesticks[-1] if candlesticks else {}

        # Prepare compact response
        response_data = {
            "status": "success",
            "data": {
                "asset": asset,
                "equity": equity,
                "market": market,
                "exchange": exchange,
                "interval": bar,
                "latest_candle": latest_candle,
                "candlesticks_count": len(candlesticks),
                "indicators": compact_indicators,
                "latest_indicator_values": latest_values
            },
            "metadata": {
                "total_records": len(candlesticks),
                "limit": limit,
                "requested_indicators": indicator_name,
                "response_type": "compact",
                "indicators_count": len(compact_indicators)
            }
        }

        return jsonify(response_data)

    except Exception as e:
        return jsonify({
            "error": f"Failed to retrieve indicators: {str(e)}",
            "message": "Please check your parameters and try again"
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "service": "trading-analysis-api"})

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # For development
    app.run(debug=True, host='0.0.0.0', port=5000)

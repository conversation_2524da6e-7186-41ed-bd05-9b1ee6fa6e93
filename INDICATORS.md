# Technical Indicators Reference

This document lists all available technical indicators in the `/indicators` endpoint.

## Usage

```bash
curl -X POST http://localhost:5000/indicators \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "bar": "1H",
    "limit": 100,
    "indicatorName": "all"
  }'
```

## Available Indicators

### Volume Indicators (9 total)

| ID | Name | Function Name | Description |
|----|------|---------------|-------------|
| 1 | Money Flow Index (MFI) | `money_flow_index` | Measures buying and selling pressure |
| 2 | Accumulation/Distribution Index (ADI) | `acc_dist_index` | Volume-based indicator showing money flow |
| 3 | On-Balance Volume (OBV) | `on_balance_volume` | Momentum indicator using volume |
| 4 | Chaikin Money Flow (CMF) | `chaikin_money_flow` | Measures money flow over a period |
| 5 | Force Index (FI) | `force_index` | Uses price and volume to assess power |
| 6 | Ease of Movement (EoM, EMV) | `ease_of_movement` | Relates price change to volume |
| 6b | SMA Ease of Movement | `sma_ease_of_movement` | Smoothed version of EoM |
| 7 | Volume-price Trend (VPT) | `volume_price_trend` | Combines price and volume |
| 8 | Negative Volume Index (NVI) | `negative_volume_index` | Focuses on down-volume days |
| 9 | Volume Weighted Average Price (VWAP) | `volume_weighted_average_price` | Average price weighted by volume |

### Volatility Indicators (5 total)

| ID | Name | Function Name | Description |
|----|------|---------------|-------------|
| 10 | Average True Range (ATR) | `average_true_range` | Measures market volatility |
| 11 | Bollinger Bands (BB) | `bollinger_hband`, `bollinger_lband`, `bollinger_mavg`, etc. | Price volatility bands |
| 12 | Keltner Channel (KC) | `keltner_channel_hband`, `keltner_channel_lband`, etc. | Volatility-based channels |
| 13 | Donchian Channel (DC) | `donchian_channel_hband`, `donchian_channel_lband`, etc. | Price channel indicator |
| 14 | Ulcer Index (UI) | `ulcer_index` | Measures downside risk |

### Trend Indicators (15 total)

| ID | Name | Function Name | Description |
|----|------|---------------|-------------|
| 15 | Simple Moving Average (SMA) | `sma_indicator` | Average price over period |
| 16 | Exponential Moving Average (EMA) | `ema_indicator` | Weighted moving average |
| 17 | Weighted Moving Average (WMA) | `wma_indicator` | Linearly weighted moving average |
| 18 | Moving Average Convergence Divergence (MACD) | `macd`, `macd_diff`, `macd_signal` | Trend-following momentum indicator |
| 19 | Average Directional Movement Index (ADX) | `adx`, `adx_neg`, `adx_pos` | Trend strength indicator |
| 20 | Vortex Indicator (VI) | `vortex_indicator_neg`, `vortex_indicator_pos` | Trend reversal indicator |
| 21 | Trix (TRIX) | `trix` | Triple exponential moving average |
| 22 | Mass Index (MI) | `mass_index` | Identifies trend reversals |
| 23 | Commodity Channel Index (CCI) | `cci` | Momentum-based oscillator |
| 24 | Detrended Price Oscillator (DPO) | `dpo` | Removes trend from price |
| 25 | KST Oscillator (KST) | `kst`, `kst_sig` | Momentum oscillator |
| 26 | Ichimoku Kinkō Hyō | `ichimoku_a`, `ichimoku_b`, `ichimoku_base_line`, etc. | Japanese trend indicator |
| 27 | Parabolic Stop And Reverse (SAR) | `psar_down`, `psar_up`, etc. | Trend-following indicator |
| 28 | Schaff Trend Cycle (STC) | `stc` | Cyclical trend indicator |
| 29 | Aroon Indicator | `aroon_down`, `aroon_up` | Trend strength and direction |

### Momentum Indicators (11 total)

| ID | Name | Function Name | Description |
|----|------|---------------|-------------|
| 30 | Relative Strength Index (RSI) | `rsi` | Momentum oscillator (0-100) |
| 31 | Stochastic RSI (SRSI) | `stochrsi`, `stochrsi_d`, `stochrsi_k` | RSI-based stochastic |
| 32 | True Strength Index (TSI) | `tsi` | Momentum oscillator |
| 33 | Ultimate Oscillator (UO) | `ultimate_oscillator` | Multi-timeframe momentum |
| 34 | Stochastic Oscillator (SR) | `stoch`, `stoch_signal` | Momentum indicator |
| 35 | Williams %R (WR) | `williams_r` | Momentum indicator |
| 36 | Awesome Oscillator (AO) | `awesome_oscillator` | Momentum indicator |
| 37 | Kaufman's Adaptive Moving Average (KAMA) | `kama` | Adaptive moving average |
| 38 | Rate of Change (ROC) | `roc` | Price change percentage |
| 39 | Percentage Price Oscillator (PPO) | `ppo`, `ppo_hist`, `ppo_signal` | MACD-like oscillator |
| 40 | Percentage Volume Oscillator (PVO) | `pvo`, `pvo_hist`, `pvo_signal` | Volume-based oscillator |

### Other Indicators (3 total)

| ID | Name | Function Name | Description |
|----|------|---------------|-------------|
| 41 | Daily Return (DR) | `daily_return` | Daily price change percentage |
| 42 | Daily Log Return (DLR) | `daily_log_return` | Logarithmic daily returns |
| 43 | Cumulative Return (CR) | `cumulative_return` | Cumulative price returns |

## Request Examples

### Get All Indicators
```json
{
  "asset": "Cryptocurrency",
  "equity": "BTC",
  "market": "USDT",
  "exchange": "Binance",
  "bar": "1H",
  "limit": 100,
  "indicatorName": "all"
}
```

### Get Specific Indicators
```json
{
  "asset": "Cryptocurrency",
  "equity": "ETH",
  "market": "USDT",
  "exchange": "OKX",
  "bar": "5m",
  "limit": 50,
  "indicatorName": "rsi,macd,bollinger_hband,sma_indicator"
}
```

### Get Volume Indicators Only
```json
{
  "asset": "Stocks",
  "equity": "AAPL",
  "market": "S&P 500",
  "bar": "1D",
  "limit": 100,
  "indicatorName": "money_flow_index,on_balance_volume,chaikin_money_flow"
}
```

## Response Format

```json
{
  "status": "success",
  "data": {
    "asset": "Cryptocurrency",
    "equity": "BTC",
    "market": "USDT",
    "exchange": "Binance",
    "interval": "1H",
    "candlesticks": [
      {
        "timestamp": "2024-01-01T00:00:00",
        "open": 42000.0,
        "high": 42500.0,
        "low": 41800.0,
        "close": 42300.0,
        "volume": 1234.56
      }
    ],
    "indicators": {
      "volume": {
        "money_flow_index": [45.2, 48.1, ...],
        "on_balance_volume": [1000, 1200, ...]
      },
      "volatility": {
        "average_true_range": [120.5, 115.2, ...],
        "bollinger_hband": [43000, 43200, ...]
      },
      "trend": {
        "sma_indicator": [42100, 42150, ...],
        "macd": [12.5, 15.2, ...]
      },
      "momentum": {
        "rsi": [65.2, 68.1, ...],
        "stoch": [45.2, 48.7, ...]
      },
      "others": {
        "daily_return": [0.012, -0.005, ...]
      }
    }
  },
  "metadata": {
    "total_records": 100,
    "limit": 100,
    "requested_indicators": "all",
    "has_more": false
  }
}
```

## Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `asset` | string | No | "Cryptocurrency" | Asset type |
| `equity` | string | Yes | - | Specific instrument |
| `market` | string | No | "USDT" | Market type |
| `exchange` | string | No | "Binance" | Exchange (for crypto) |
| `bar` | string | No | "1m" | Time interval |
| `limit` | integer | No | 100 | Number of candlesticks (max 100) |
| `indicatorName` | string | No | "all" | Indicators to calculate |
| `after` | string | No | - | Pagination (future feature) |
| `before` | string | No | - | Pagination (future feature) |

## Supported Time Intervals

- `1m` - 1 minute
- `3m` - 3 minutes  
- `5m` - 5 minutes
- `15m` - 15 minutes
- `30m` - 30 minutes
- `1H` - 1 hour
- `2H` - 2 hours
- `4H` - 4 hours
- `1D` - 1 day

## Documentation

For more information about the technical analysis library used, visit:
https://technical-analysis-library-in-python.readthedocs.io/en/latest/

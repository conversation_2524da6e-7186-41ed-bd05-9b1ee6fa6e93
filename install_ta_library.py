#!/usr/bin/env python3
"""
Install the ta (Technical Analysis) library
"""

import subprocess
import sys
import os

def install_ta_library():
    """Install the ta library using pip"""
    print("📦 Installing Technical Analysis (ta) library...")
    
    try:
        # Check if we're in a virtual environment
        venv_path = os.environ.get('VIRTUAL_ENV')
        if venv_path:
            print(f"✅ Using virtual environment: {venv_path}")
            pip_cmd = os.path.join(venv_path, 'bin', 'pip')
        else:
            print("⚠️  No virtual environment detected, using system pip")
            pip_cmd = 'pip'
        
        # Install ta library
        print("Installing ta==0.10.2...")
        result = subprocess.run([pip_cmd, 'install', 'ta==0.10.2'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Successfully installed ta library!")
            print("\nInstalled packages:")
            print(result.stdout)
        else:
            print("❌ Failed to install ta library")
            print("Error:", result.stderr)
            return False
        
        # Verify installation
        print("\n🔍 Verifying installation...")
        try:
            import ta
            print("✅ ta library imported successfully")

            # Test basic functionality
            import pandas as pd
            import numpy as np
            
            # Create sample data
            dates = pd.date_range('2024-01-01', periods=50, freq='D')
            sample_data = pd.DataFrame({
                'high': np.random.uniform(100, 110, 50),
                'low': np.random.uniform(90, 100, 50),
                'close': np.random.uniform(95, 105, 50),
                'volume': np.random.uniform(1000, 5000, 50)
            }, index=dates)
            
            # Test RSI calculation
            rsi = ta.momentum.RSIIndicator(close=sample_data['close']).rsi()
            print(f"✅ RSI calculation test: {len(rsi)} values generated")
            
            # Test MACD calculation
            macd = ta.trend.MACD(close=sample_data['close']).macd()
            print(f"✅ MACD calculation test: {len(macd)} values generated")
            
            print("\n🎉 ta library is working correctly!")
            return True
            
        except ImportError as e:
            print(f"❌ Failed to import ta library: {e}")
            return False
        except Exception as e:
            print(f"❌ Error testing ta library: {e}")
            return False
    
    except Exception as e:
        print(f"❌ Installation failed: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_packages = ['pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is available")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing_packages)}")
        print("Please install them first:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Technical Analysis Library Installation")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first")
        sys.exit(1)
    
    # Install ta library
    if install_ta_library():
        print("\n" + "=" * 50)
        print("✅ Installation completed successfully!")
        print("\n💡 You can now use the /indicators endpoint with all technical indicators")
        print("\nNext steps:")
        print("1. Start the API: python Trade.py")
        print("2. Test the endpoint: python test_indicators_endpoint.py")
        print("3. See examples: python examples/indicators_usage.py")
    else:
        print("\n❌ Installation failed")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the correct virtual environment")
        print("2. Try: pip install --upgrade pip")
        print("3. Try: pip install ta==0.10.2 --no-cache-dir")
        sys.exit(1)
